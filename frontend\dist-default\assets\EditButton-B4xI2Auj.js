import{j as e,am as o,Y as r,c1 as t,cE as l}from"./vendor-DvOQ6qlC.js";import{t as n}from"./index-DOw-VlvP.js";function s({onClick:l,disabled:s=!1,buttonStyle:i={},iconStyle:d={}}){return e.jsx(o,{enterDelay:300,title:"Delete",placement:"bottom",children:e.jsx("span",{children:e.jsx(r,{onClick:l,disabled:s,sx:{background:"#E600000D",border:`1px solid ${n.palette.custom.borderColor}`,borderRadius:"5px",padding:"8px",...i},children:e.jsx(t,{color:"error",sx:{fontSize:"18px",...d}})})})})}function i({onClick:t,buttonStyle:s={},iconStyle:i={},...d}){return e.jsx(o,{enterDelay:300,title:"Edit",placement:"bottom",children:e.jsx(r,{onClick:t,sx:{border:`1px solid ${n.palette.custom.borderColor}`,borderRadius:"5px",padding:"5px",color:"#9747FF",bgcolor:"#9747FF0D",...s},...d,children:e.jsx(l,{fontSize:"small",sx:{...i}})})})}export{s as D,i as E};
