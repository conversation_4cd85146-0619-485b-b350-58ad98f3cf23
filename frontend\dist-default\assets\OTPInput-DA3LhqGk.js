import{r as e,ay as t,V as n,j as a,G as o,T as r,W as s,L as i,a0 as c}from"./vendor-DvOQ6qlC.js";import{u as l,t as d,e as p}from"./index-DOw-VlvP.js";import"./utils-guRmN1PB.js";import"./maps-R0vlfPHe.js";import"./charts-Bh3hGOgg.js";const u=()=>{const[u,m]=e.useState(["","","","","",""]),[x,f]=e.useState(""),[h,g]=e.useState(!1),[j,y]=e.useState(0),[F,v]=e.useState(!1),[S,b]=e.useState(""),W=t(),{login:C}=l(),P=n(),{username:T,password:D}=W.state||{},E=e.useRef();e.useEffect((()=>{x&&(clearTimeout(E.current),E.current=setTimeout((()=>f("")),3e3))}),[x]);const w=async()=>{try{const e=await p.post("/users/sendEmailOTP",{username:T});e.data.email&&b(e.data.email),y(60),v(!0)}catch(e){f(e.response?.data?.message||"An error occurred")}};return e.useEffect((()=>{T?w():P("/login")}),[T]),e.useEffect((()=>{if(F){const e=setInterval((()=>{y((t=>t<=1?(clearInterval(e),v(!1),0):t-1))}),1e3);return()=>clearInterval(e)}}),[F]),a.jsxs(o,{container:!0,flexDirection:"column",gap:"32px",children:[a.jsxs(o,{container:!0,flexDirection:"column",color:"#FFFFFF",children:[a.jsx(r,{component:"h3",fontWeight:"600",fontSize:"46px",textAlign:"center",children:"Enter OTP Code"}),a.jsx(r,{component:"p",fontWeight:"500",fontSize:"24px",children:"Check your email"}),a.jsxs(r,{component:"p",fontWeight:"400",fontSize:"16px",children:["We've sent a 6-digit confirmation OTP code to ",a.jsx("strong",{children:S||T}),". Make sure you enter the correct code."]})]}),a.jsxs(o,{container:!0,flexDirection:"column",component:"form",onSubmit:async e=>{e.preventDefault(),g(!0);const t=u.join("");try{if(!T&&!D)return f("Username and password required. Please navigate to login page");await p.post("/users/emailOTPVerification",{otp:Number(t),username:T});if(await C({username:T,password:D})){const e=sessionStorage.getItem("eventPath");P(e||"/dashboard/stream")}f("")}catch(n){f(n.response?.data?.message||"An error occurred")}finally{g(!1)}},gap:4,children:[a.jsx(o,{container:!0,justifyContent:"center",onPaste:e=>{const t=e.clipboardData.getData("text");if(/^\d{6}$/.test(t)){const e=t.split("");m(e),e.forEach(((t,n)=>{document.getElementById(`otp-input-${n}`).value=e[n]}))}e.preventDefault()},gap:"10px",children:u.map(((e,t)=>a.jsx(s,{id:`otp-input-${t}`,type:"text",value:e,onChange:e=>((e,t)=>{const n=e.target.value;if(/^[0-9]?$/.test(n)){const e=[...u];e[t]=n,m(e),n&&t<u.length-1&&document.getElementById(`otp-input-${t+1}`).focus()}})(e,t),maxLength:"1",variant:"outlined",sx:{width:"13%",backgroundColor:"transparent",border:"1px solid #818994",borderRadius:"5px",maxWidth:"80px",minWidth:"30px"},inputProps:{autoComplete:"off",inputMode:"numeric",pattern:"[0-9]*","aria-label":`OTP digit ${t+1}`,style:{color:"#FFFFFF",textAlign:"center",padding:0,height:"60px"},onKeyDown:e=>((e,t)=>{"Backspace"===e.key&&!u[t]&&t>0&&document.getElementById("otp-input-"+(t-1)).focus()})(e,t)}},t)))}),x&&a.jsx(o,{children:a.jsx(r,{color:"error",children:x})}),a.jsx(o,{children:a.jsx(i,{type:"submit",variant:"contained",className:"btn-login",fullWidth:!0,sx:{color:d.palette.primary.main,padding:"15px 10px",fontSize:"24px",fontWeight:"600"},disabled:h,endIcon:h&&a.jsx(c,{size:20}),children:"Confirm OTP"})}),a.jsx(o,{children:a.jsx(i,{variant:"text",fullWidth:!0,sx:{textDecoration:F?"none !important":"underline",color:F?"#717171 !important":"#FFFFFF",padding:0,fontSize:"20px",fontWeight:"500",textTransform:"none"},onClick:w,disabled:F,children:F?`Resend Code (${Math.floor(j/60)}:${String(j%60).padStart(2,"0")})`:"Resend Code"})})]})]})};export{u as default};
