import mongoose from "mongoose";
import { ICoordinates } from "./VesselLocation";

export interface IVesselInfo {
    vessel_id: string;
    unit_id: string | null;
    name: string;
    thumbnail_s3_key: string | null;
    is_active: boolean;
    region?: string | null;
    is_live: boolean;
    timezone?: string | null;
    region_group_id?: mongoose.Types.ObjectId | null;
    home_port_location?: ICoordinates | null;
}
