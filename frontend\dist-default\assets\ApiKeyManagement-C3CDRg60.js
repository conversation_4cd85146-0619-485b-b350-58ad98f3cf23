import{r as e,j as t,bF as s,G as n,W as i,L as a,T as o,ba as r,b5 as l,Y as d,aw as c,ax as x,a0 as p,bX as u,bQ as h,bR as m,R as f,bS as g,bT as j,bZ as y,b_ as w,an as b,x as v,I as C,bY as _,am as k,b$ as S,b8 as F,c0 as A,c1 as z,bJ as K,bb as R,bd as W,ac as I,bI as D,ag as P,af as B,bf as M,X as E,bO as $}from"./vendor-DvOQ6qlC.js";import{e as T,D as N,u as L,a as q,t as G,b as O,p as U}from"./index-DOw-VlvP.js";import{u as V}from"./AppHook-Pvyw2N1K.js";import{M as Z}from"./ModalContainer-B-bBgs2N.js";import{u as H}from"./VesselInfoHook-iC2umhbJ.js";import{u as X}from"./GroupRegionHook-BQhJsHqX.js";import{D as Y,E as Q}from"./EditButton-B4xI2Auj.js";import{M as J}from"./MultiSelect-D8-gMvUK.js";import{D as ee}from"./DataGrid-DsxndKbs.js";import"./utils-guRmN1PB.js";import"./maps-R0vlfPHe.js";import"./charts-Bh3hGOgg.js";const te=new class{async fetchAll(){return(await T.get("/apiKeys")).data}async create({description:e,email:t}){return(await T.post("/apiKeys",{description:e,email:t},{meta:{showSnackbar:!0}})).data}async update({id:e,email:t,description:s}){return(await T.patch(`/apiKeys/${e}/details`,{email:t,description:s},{meta:{showSnackbar:!0}})).data}async delete({id:e}){return(await T.delete(`/apiKeys/${e}`,{meta:{showSnackbar:!0}})).data}async revoke({id:e,revoke:t}){return(await T.patch(`/apiKeys/${e}/revoke`,{revoke:t},{meta:{showSnackbar:!0}})).data}async updateAllowedEndpoints({id:e,allowed_endpoints:t}){return(await T.patch(`/apiKeys/${e}/allowedEndpoints`,{allowed_endpoints:t},{meta:{showSnackbar:!0}})).data}async updateAllowedVessels({id:e,allowed_vessels:t}){return(await T.patch(`/apiKeys/${e}/allowedVessels`,{allowed_vessels:t},{meta:{showSnackbar:!0}})).data}},se=({showAddKey:o,setShowAddKey:r,setAdding:l,fetchKeys:d})=>{const[c,x]=e.useState(""),[p,u]=e.useState(""),[h,m]=e.useState(""),[f,g]=e.useState(!1),j=()=>{r(!1),x(""),u(""),m(""),g(!1)},y=e=>e&&!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(e)?"Please enter a valid email address":"";return t.jsx(s,{open:o,onClose:j,children:t.jsx(Z,{title:"Create API Key",onClose:j,children:t.jsxs(n,{container:!0,flexDirection:"column",gap:2,children:[t.jsx(n,{children:t.jsx(i,{value:c,sx:{minWidth:{xs:250,sm:500}},onChange:e=>x(e.target.value),label:"Description",variant:"filled",required:!0})}),t.jsx(n,{children:t.jsx(i,{value:p,sx:{minWidth:{xs:250,sm:500}},onChange:e=>{const t=e.target.value;u(t),f&&m(y(t))},label:"Email (optional)",variant:"filled",type:"email",error:!!h,helperText:h})}),t.jsx(n,{justifyContent:"center",display:"flex",children:t.jsx(a,{disabled:!c||!!h,variant:"contained",onClick:async()=>{try{g(!0);const e=y(p);if(e)return void m(e);l(!0),await te.create({description:c,...p&&{email:p}}),d(),x(""),u(""),j()}catch(e){}finally{l(!1)}},children:"Submit"})})]})})})},ne=({deleteKey:e,setDeleteKey:i,setDeleting:r,fetchKeys:l})=>{const d=()=>{i()};return t.jsx(s,{open:!!e,onClose:d,children:t.jsx(Z,{title:"Delete API Key",headerPosition:"center",children:t.jsxs(n,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:"auto"},children:[t.jsx(n,{children:t.jsxs(o,{fontWeight:"500",fontSize:"15px",children:["Are you sure you want to delete API key #",e?.serial,"?"]})}),t.jsxs(n,{container:!0,gap:1,justifyContent:"center",children:[t.jsx(n,{justifyContent:"center",display:"flex",children:t.jsx(a,{variant:"contained",className:"btn-cancel",onClick:d,children:"Cancel"})}),t.jsx(n,{justifyContent:"center",display:"flex",children:t.jsx(a,{variant:"contained",color:"error",onClick:async()=>{try{r(e._id),await te.delete({id:e._id}),l(),d()}catch(t){}finally{r(null)}},sx:{textTransform:"none",padding:"10px 24px"},children:"Delete"})})]})]})})})},ie=({revokeKey:e,setRevokeKey:i,setRevoking:r,fetchKeys:l})=>{const d=()=>{i()};return t.jsx(s,{open:!!e,onClose:d,children:t.jsx(Z,{title:(e?.is_revoked?"Restore":"Revoke")+" Key",headerPosition:"center",children:t.jsxs(n,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:"auto"},children:[t.jsx(n,{children:t.jsxs(o,{fontWeight:"500",fontSize:"15px",children:["Are you sure you want to ",e?.is_revoked?"restore":"revoke"," API key #",e?.serial,"?"]})}),t.jsxs(n,{container:!0,gap:1,justifyContent:"center",children:[t.jsx(n,{justifyContent:"center",display:"flex",children:t.jsx(a,{variant:"contained",onClick:d,className:"btn-cancel",children:"Cancel"})}),t.jsx(n,{justifyContent:"center",display:"flex",children:t.jsx(a,{variant:"contained",sx:{textTransform:"none",padding:"10px 24px"},color:e?.is_revoked?"success":"warning",onClick:async()=>{try{r(e._id),await te.revoke({id:e._id,revoke:!e.is_revoked}),l(),d()}catch(t){}finally{r(null)}},children:e?.is_revoked?"Restore":"Revoke"})})]})]})})})},ae=({updateKeyAccess:i,setUpdateKeyAccess:o,endpoints:u,fetchKeys:h})=>{const[m,f]=e.useState(!1),[g,j]=e.useState(!1),[y,w]=e.useState(i),[b,v]=e.useState(!1),[C,_]=e.useState([]);e.useEffect((()=>{w(i)}),[i]),e.useEffect((()=>{if(u.length>0){const e=u.filter((e=>e.is_accessible));_([...new Set(e.map((e=>e.category)))])}}),[u]);const k=()=>{o(),f(!1),j(!1)},S=e=>u.filter((t=>t.category===e)).every((e=>e.is_public||y?.allowed_endpoints.includes(e.endpoint_id))),F=e=>{const t=u.filter((t=>t.category===e)),s=t.filter((e=>e.is_public||y?.allowed_endpoints.includes(e.endpoint_id)));return s.length>0&&s.length<t.length},A=e=>u.filter((t=>t.category===e)).every((e=>e.is_public)),z=[...new Set(u.filter((e=>e.is_accessible)).map((e=>e.category)))].sort(((e,t)=>{const s=A(e),n=A(t);return s&&!n?1:!s&&n?-1:0}));return t.jsx(s,{open:!!y,onClose:k,children:t.jsx(Z,{title:`Update API Access #${y?.serial}`,onClose:k,children:t.jsxs(n,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},children:[t.jsx(n,{container:!0,overflow:"auto",maxHeight:"50vh",padding:2,border:e=>`1px solid ${e.palette.custom.borderColor}`,borderRadius:"5px",children:z.map(((e,s)=>{const i=u.filter((t=>t.category===e&&t.is_accessible));return 0===i.length?null:t.jsxs(n,{container:!0,flexDirection:"column",width:"100%",children:[t.jsxs(n,{container:!0,justifyContent:"space-between",children:[t.jsx(n,{children:t.jsx(r,{label:e,sx:{"& .MuiTypography-root":{fontSize:"16px",fontWeight:"400"}},control:t.jsx(l,{size:"small",indeterminate:F(e),checked:S(e),onChange:t=>((e,t)=>{f(!0),j(!0);const s=u.filter((e=>e.category===t)).map((e=>e.endpoint_id));w((t=>e.target.checked?{...t,allowed_endpoints:[...new Set(t.allowed_endpoints.concat(s))]}:{...t,allowed_endpoints:t.allowed_endpoints.filter((e=>!s.includes(e)))}))})(t,e),disabled:A(e)})})}),t.jsx(n,{children:t.jsx(d,{onClick:()=>_((t=>t.includes(e)?t.filter((t=>t!==e)):t.concat(e))),children:C.includes(e)?t.jsx(c,{}):t.jsx(x,{})})})]}),t.jsx(n,{container:!0,flexDirection:"column",display:C.includes(e)?"flex":"none",sx:{background:e=>e.palette.custom.borderColor},padding:"10px 20px",borderRadius:"10px",children:i.map(((e,s)=>t.jsx(n,{children:t.jsx(r,{label:e.name,sx:{"& .MuiTypography-root":{fontSize:"16px",fontWeight:"400"}},control:t.jsx(l,{disabled:e.is_public,size:"small",checked:e.is_public||y?.allowed_endpoints.includes(e.endpoint_id),onChange:t=>((e,t)=>{f(!0),j(!0),w((s=>e.target.checked?{...s,allowed_endpoints:s.allowed_endpoints.concat(t)}:{...s,allowed_endpoints:s.allowed_endpoints.filter((e=>e!==t))}))})(t,e.endpoint_id)})})},s)))})]},s)}))}),t.jsxs(n,{container:!0,gap:1,justifyContent:"flex-end",children:[t.jsx(n,{children:t.jsx(a,{disabled:!g,onClick:()=>{w(i),f(!1),j(!1)},variant:"outlined",children:"Reset"})}),t.jsx(n,{children:t.jsx(a,{disabled:!m||b,startIcon:b&&t.jsx(p,{size:18}),variant:"contained",onClick:async()=>{try{v(!0),await te.updateAllowedEndpoints({id:y._id,allowed_endpoints:y.allowed_endpoints}),f(!1),j(!1),h(),k()}catch(e){}finally{h(),v(!1)}},children:"Save"})})]})]})})})},oe=({apiKey:o,showEditModal:r,setShowEditModal:l,setUpdating:d,fetchKeys:c})=>{const[x,p]=e.useState(""),[u,h]=e.useState(""),[m,f]=e.useState(""),[g,j]=e.useState(""),[y,w]=e.useState(!1);e.useEffect((()=>{o&&(p(o?.email||""),h(o?.description||""))}),[o]);const b=()=>{l(!1),p(""),h(""),f(""),j(""),w(!1),d(null)},v=e=>e?/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(e)?"":"Please enter a valid email address":"",C=e=>e&&""!==e.trim()?"":"Description is required",_=!(u!==(o?.description||"")||x!==(o?.email||""))||!!m||!!g;return t.jsx(s,{open:r,onClose:b,children:t.jsx(Z,{title:"Update API Key Details",onClose:b,children:t.jsxs(n,{container:!0,flexDirection:"column",gap:2,children:[t.jsx(n,{children:t.jsx(i,{value:u,sx:{minWidth:{xs:250,sm:500}},onChange:e=>{const t=e.target.value;h(t),y&&j(C(t))},label:"Description",variant:"filled",required:!0,error:!!g,helperText:g})}),t.jsx(n,{children:t.jsx(i,{value:x,sx:{minWidth:{xs:250,sm:500}},onChange:e=>{const t=e.target.value;p(t),y&&f(v(t))},label:"Email (optional)",variant:"filled",type:"email",error:!!m,helperText:m})}),t.jsx(n,{justifyContent:"center",display:"flex",children:t.jsx(a,{variant:"contained",onClick:async()=>{w(!0);const e=v(x),t=C(u);if(f(e),j(t),!e&&!t)try{d(o._id),await te.update({id:o._id,description:u,...""!==x.trim()&&{email:x}}),c(),b()}catch(s){}finally{d(null)}},disabled:_,children:"Update"})})]})})})};function re({api_key:s,vessels:n,regionGroups:i,disabled:a,fetchKeys:o}){const[r,l]=e.useState([]),[d,c]=e.useState(!1),x=e.useMemo((()=>n.filter((e=>e.region_group_id&&!1!==e.is_active)).map((e=>({...e,region_group_object:i.find((t=>t._id===e.region_group_id))}))).sort(((e,t)=>{const s=e.region_group_object?.name?.toLowerCase()||"",n=t.region_group_object?.name?.toLowerCase()||"";if(s<n)return-1;if(s>n)return 1;const i=e.name?.toLowerCase()||e.vessel_id,a=t.name?.toLowerCase()||t.vessel_id;return i.localeCompare(a)}))),[n,i]);e.useEffect((()=>{u()}),[s,n]);const u=()=>l(s.allowed_vessels||[]),h=e.useMemo((()=>{if(r.length!==(s.allowed_vessels||[]).length)return!0;const e=new Set(s.allowed_vessels||[]);return r.some((t=>!e.has(t)))}),[r,s]);return d?t.jsx(p,{}):t.jsx(J,{loading:0===n.length,options:x,value:r,disabled:a||d,multiple:!0,disableCloseOnSelect:!0,groupBy:e=>e.region_group_object?.name,label:`${(s.allowed_vessels||[]).length} selected`,getOptionLabel:e=>e.name,isOptionEqualToValue:(e,t)=>t.includes(e.vessel_id),renderTags:()=>null,onChange:(e,t)=>l(t.map((e=>"string"==typeof e?e:e.vessel_id))),onClose:()=>{h&&(async()=>{try{c(!0);const e=r.filter((e=>n.find((t=>t.vessel_id===e))));await te.updateAllowedVessels({id:s._id,allowed_vessels:e}),o()}catch(e){u()}finally{c(!1),o()}})()}})}function le({searchQuery:s,showAddKey:i,setShowAddKey:a}){const{isMobile:r,timezone:l}=V(),c=N(),{user:x}=L(),{vesselInfo:P,fetchVesselsInfo:B}=H(),{regions:M,fetchRegions:E}=X(),[$,U]=e.useState(!0),[Z,J]=e.useState([]),[le,de]=e.useState([]),[ce,xe]=e.useState(),[pe,ue]=e.useState(null),[he,me]=e.useState(!1),[fe,ge]=e.useState(),[je,ye]=e.useState(),[we,be]=e.useState(),[ve,Ce]=e.useState(),[_e,ke]=e.useState(),[Se,Fe]=e.useState(1),[Ae,ze]=e.useState(10),[Ke,Re]=e.useState({}),[We,Ie]=e.useState([]),[De,Pe]=e.useState([]),[Be,Me]=e.useState(null),[Ee,$e]=e.useState(null);e.useEffect((()=>{Oe()}),[M]),e.useEffect((()=>{Te(),Ne()}),[]),e.useEffect((()=>{Te()}),[he,je,ve,Ee]),e.useEffect((()=>{_e&&ke((e=>Z.find((t=>t._id===e._id))))}),[Z]),e.useEffect((()=>{let e=[];s.trim()?(e=Z.filter((e=>{const t=e.description?e.description.toLowerCase():"",n=e.requests?parseInt(e.requests,10):0,i=parseInt(s,10);return t.includes(s.toLowerCase())||!isNaN(i)&&n>=i})),de(e)):de(Z),Fe(1)}),[s,Z,ce]),e.useEffect((()=>{Ge()}),[P]);const Te=async()=>{try{U(!0);const e=await te.fetchAll();if(Array.isArray(e)&&e.length>0){const t=e.map(((e,t)=>({serial:t+1,...e})));J(t)}else J([]),c("No data found for keys",{variant:"warning"})}catch(e){J([]),c("Something went wrong",{variant:"error"})}finally{U(!1)}},Ne=async()=>{try{U(!0);const{data:e}=await T.get("/apiEndpoints");Array.isArray(e)&&e.length>0?xe(e):(xe([]),c("No data found for endpoints",{variant:"warning"}))}catch(e){xe([]),c("Something went wrong",{variant:"error"})}finally{U(!1)}},Le=(e,t)=>{Fe(t)},qe=e=>{ze(e.target.value)},Ge=async()=>{try{if(P){const e=P.filter((e=>e.is_active));Ie(e)}else B()}catch(e){}},Oe=async()=>{M?Pe(M):E()},Ue=[...[{field:"serial",headerName:"",maxWidth:50,renderCell:({row:e})=>e.serial+"."},{field:"api_key",headerName:"API Key",flex:1,minWidth:360,renderCell:({row:e})=>t.jsxs(n,{container:!0,justifyContent:"space-between",alignItems:"center",gap:2,flexWrap:"nowrap",overflow:"auto",children:[t.jsx(n,{children:t.jsx(o,{children:pe===e._id?e.api_key:Array.from({length:16}).fill("*").join("")})}),t.jsx(n,{children:t.jsx(d,{onClick:()=>ue((t=>t===e._id?null:e._id)),children:pe===e._id?t.jsx(y,{}):t.jsx(w,{})})})]})},{field:"description",headerName:"Description",flex:1,minWidth:250,renderCell:({row:e})=>t.jsx(n,{container:!0,alignItems:"center",height:"100%",gap:1,children:t.jsx(k,{enterDelay:300,title:e.description,placement:"bottom",children:t.jsx(o,{fontSize:"14px",fontWeight:400,children:e.description.length>20?e.description.slice(0,30)+"...":e.description})})})},{field:"email",headerName:"Email",flex:1,minWidth:200,renderCell:({row:e})=>t.jsx(n,{container:!0,alignItems:"center",height:"100%",gap:1,children:t.jsx(o,{fontSize:"14px",fontWeight:400,children:e.email||"--"})})},{field:"allowed_endpoints",headerName:"API Access",minWidth:220,renderCell:({row:e})=>t.jsxs(n,{container:!0,justifyContent:"space-between",alignItems:"center",gap:2,flexWrap:"nowrap",overflow:"auto",sx:{cursor:"pointer"},onClick:()=>ke(e),children:[t.jsx(n,{children:t.jsx(o,{fontWeight:"400",sx:{textDecoration:"underline"},children:"Manage Permissions"})}),t.jsx(n,{children:t.jsx(k,{enterDelay:300,title:"Manage Permissions",placement:"bottom",children:t.jsx(d,{children:t.jsx(S,{sx:{color:G.palette.custom.mainBlue}})})})})]}),sortable:!1},{field:"allowed_vessels",headerName:"Provisioned Vessels",minWidth:300,renderCell:({row:e})=>{const s=Z.find((t=>t._id===e._id));return s?t.jsx("div",{style:{maxWidth:200},children:t.jsx(re,{disabled:!1,api_key:s,vessels:We,regionGroups:De,fetchKeys:Te})}):null}},{field:"requests",minWidth:100,headerName:"Total Requests"},{field:"creation_timestamp",headerName:"Created",minWidth:150,valueGetter:e=>q(e).tz(l).format(O.dateTimeFormat(x,{exclude_seconds:!0,exclude_hours:!0,exclude_minutes:!0}))},{field:"created_by",headerName:"Created By",minWidth:150,valueGetter:e=>e?.name},{field:"last_used",headerName:"Last Used",minWidth:150,valueGetter:e=>e?q(e).tz(l).format(O.dateTimeFormat(x,{exclude_seconds:!0,exclude_hours:!0,exclude_minutes:!0})):"--"},{field:"actions",headerName:"Actions",minWidth:200,headerAlign:"center",renderCell:e=>t.jsxs(n,{container:!0,justifyContent:"center",gap:1,children:[t.jsx(n,{children:ve===e.row._id?t.jsx(p,{size:18}):t.jsx(k,{enterDelay:300,title:e.row.is_revoked?"Restore Access":"Revoke Access",placement:"bottom",children:t.jsx(d,{onClick:()=>be(e.row),sx:{background:e.row.is_revoked?"#0478570D":"#B453091A",border:`1px solid ${G.palette.custom.borderColor}`,borderRadius:"5px",padding:"8px"},children:e.row.is_revoked?t.jsx(F,{sx:{fontSize:"18px"},color:"success"}):t.jsx(A,{color:"warning",sx:{fontSize:"18px"}})})})}),t.jsx(n,{children:je===e.row._id?t.jsx(p,{size:18}):t.jsx(Y,{onClick:()=>ge(e.row)})}),t.jsx(n,{children:Ee===e.row._id?t.jsx(p,{size:18}):t.jsx(Q,{onClick:()=>Me(e.row)})})]}),sortable:!1}].map((e=>({...e,filterable:!1,resizable:!1,disableColumnMenu:!0,disableReorder:!0,disableExport:!0,flex:1})))],Ve=({page:e,rowsPerPage:s,totalRows:i,onPageChange:a,onRowsPerPageChange:l})=>{const d=(e-1)*s+1,c=Math.min(e*s,i);return t.jsxs(n,{container:!0,justifyContent:{sm:"space-between",xs:"center"},alignItems:"center",padding:"10px",backgroundColor:v(G.palette.custom.offline,.08),gap:2,sx:{borderRadius:"5px"},children:[t.jsx(n,{padding:"10px 20px",size:"auto",children:t.jsx(o,{fontSize:{xs:"12px",lg:"14px"},fontWeight:600,children:`${d} - ${c} of ${i}`})}),t.jsx(n,{size:"auto",children:t.jsx(K,{count:Math.ceil(i/s),page:e,onChange:a,shape:"rounded",siblingCount:r?0:1,boundaryCount:1,sx:{"& .MuiButtonBase-root, .MuiPaginationItem-root":{color:"#FFFFFF",minHeight:"30px",fontSize:r?"9px":"14px",borderRadius:"8px",minWidth:"32px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:v(G.palette.custom.offline,.2)},"& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected":{color:"#FFFFFF",backgroundColor:G.palette.custom.mainBlue}}})}),t.jsx(n,{justifyContent:"flex-end",display:"flex",size:"auto",children:t.jsx(R,{variant:"outlined",children:t.jsx(W,{value:s,onChange:l,sx:{"& .MuiOutlinedInput-notchedOutline":{border:"none"},"& .MuiSelect-select":{padding:"10px",fontSize:r?"12px":"16px",backgroundColor:G.palette.custom.mainBlue,borderRadius:"5px",color:"#FFFFFF",minWidth:r?0:"80px"}},children:[5,10,20].map((e=>t.jsx(I,{value:e,children:r?e:`${e} / Page`},e)))})})})]})},Ze=()=>t.jsxs(n,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",children:[t.jsx(D,{sx:{fontSize:"100px",color:G.palette.custom.borderColor}}),t.jsx(o,{variant:"h6",component:"div",gutterBottom:!0,color:G.palette.custom.borderColor,children:"No data available"})]});return t.jsxs(n,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[!r&&t.jsx(n,{overflow:"auto",size:"grow",children:t.jsx(ee,{loading:$,disableRowSelectionOnClick:!0,rows:le.slice((Se-1)*Ae,Se*Ae),columns:Ue,getRowId:e=>e._id,slots:{footer:()=>t.jsx(Ve,{page:Se,rowsPerPage:Ae,totalRows:le.length,onPageChange:Le,onRowsPerPageChange:qe}),noRowsOverlay:Ze}})}),r&&t.jsxs(n,{container:!0,overflow:"auto",display:"block",border:`1px solid ${G.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 24px",size:"grow",children:[t.jsx(n,{container:!0,paddingY:1,size:"grow",children:["#","API Keys","Details"].map(((e,s)=>t.jsx(n,{sx:{color:"#"==e?G.palette.custom.darkBlue:G.palette.custom.mainBlue,minWidth:"#"==e?"30px":"auto",flex:"API Keys"==e?1:0,padding:0,border:"none"},children:e},s)))}),$&&t.jsx(n,{container:!0,display:"flex",justifyContent:"center",alignItems:"center",height:{xs:"70%",sm:"80%"},overflow:"auto",marginBottom:2,size:"grow",children:t.jsx(p,{size:40})}),!$&&0===le.length&&t.jsx(n,{container:!0,display:"flex",justifyContent:"center",alignItems:"center",height:{xs:"70%",sm:"80%"},overflow:"auto",marginBottom:2,size:"grow",children:Ze()}),!$&&0!==le.length&&t.jsx(n,{container:!0,height:{xs:"70%",sm:"80%"},overflow:"auto",marginBottom:2,size:"grow",children:t.jsx(u,{children:t.jsx(h,{sx:{minWidth:r?0:650},"aria-labelledby":"tableTitle",children:t.jsx(m,{children:le.map((e=>t.jsxs(f.Fragment,{children:[t.jsx(g,{hover:!0,children:t.jsx(j,{colSpan:5,sx:{paddingX:"0 !important",borderBottom:0},children:t.jsx(n,{container:!0,display:"flex",children:t.jsx(n,{container:!0,display:"flex",flex:1,alignItems:"center",justifyContent:"space-between",children:t.jsxs(n,{container:!0,justifyContent:"space-between",alignItems:"center",gap:2,flexWrap:"nowrap",overflow:"auto",color:"#FFFFFF",children:[t.jsx(n,{size:"auto",children:t.jsx(o,{children:e.serial+"."})}),t.jsx(n,{size:"grow",children:t.jsx(o,{children:pe===e._id?e.api_key:Array.from({length:16}).fill("*").join("")})}),t.jsxs(n,{size:"auto",children:[t.jsx(d,{onClick:()=>ue((t=>t===e._id?null:e._id)),children:pe===e._id?t.jsx(y,{}):t.jsx(w,{})}),t.jsx(d,{onClick:()=>{return t=e,void Re((e=>e&&e._id==t._id?{}:t));var t},sx:{padding:0},children:Ke?._id==e._id?t.jsx(b,{sx:{color:v("#FFFFFF",.6),padding:0,marginRight:2}}):t.jsx(C,{sx:{color:v("#FFFFFF",.6),padding:0,marginRight:2}})})]})]})})})})}),t.jsx(g,{children:t.jsx(j,{colSpan:5,sx:{padding:0,borderBottom:0},children:t.jsx(_,{in:Ke._id==e._id,sx:{width:"100%",backgroundColor:v(G.palette.custom.offline,.08),borderRadius:"10px",padding:"0 20px","& .MuiCollapse-wrapperInner":{display:"flex",flexDirection:"column"}},children:t.jsx(n,{container:!0,sx:{display:"flex",width:"100%",paddingY:2,rowGap:"10px",flexWrap:"wrap"},children:[{label:"Description",value:Ke.description},{label:"Email",renderCell:()=>t.jsx(n,{container:!0,alignItems:"center",gap:1,children:t.jsx(o,{variant:"h6",fontSize:"16px !important",color:"#fff",children:Ke.email||"--"})})},{label:"Api Access",renderCell:()=>t.jsxs(n,{container:!0,justifyContent:"space-between",alignItems:"center",gap:2,flexWrap:"nowrap",overflow:"auto",children:[t.jsx(n,{children:t.jsx(o,{fontWeight:"400",sx:{textDecoration:"underline"},children:"Manage Permissions"})}),t.jsx(n,{children:t.jsx(k,{enterDelay:300,title:"Manage Permissions",placement:"bottom",children:t.jsx(d,{onClick:()=>ke(e),children:t.jsx(S,{sx:{color:G.palette.custom.mainBlue}})})})})]})},{label:"Total Requests",value:Ke.requests},{label:"Created",value:q(Ke.creation_timestamp).tz(l).format(O.dateTimeFormat(x,{exclude_seconds:!0}))},{label:"Last Used",value:Ke.last_used?q(Ke.last_used).tz(l).format(O.dateTimeFormat(x,{exclude_seconds:!0})):"--"},{label:"Action",renderCell:()=>t.jsxs(n,{container:!0,gap:1,children:[t.jsx(n,{children:ve===e._id?t.jsx(p,{size:18}):t.jsxs(d,{onClick:()=>be(e),sx:{background:e.is_revoked?"#0478570D":"#B453091A",border:`1px solid ${G.palette.custom.borderColor}`,borderRadius:"5px",padding:"5px"},children:[e.is_revoked?t.jsx(F,{color:"success",sx:{fontSize:"18px"}}):t.jsx(A,{color:"warning",sx:{fontSize:"18px"}}),t.jsx(o,{fontSize:"16px",marginLeft:.2,color:e.is_revoked?"#047857":"#B45309",fontWeight:"400",children:e.is_revoked?"Restore":"Revoke"})]})}),t.jsx(n,{children:je===e._id?t.jsx(p,{size:18}):t.jsxs(d,{onClick:()=>ge(e),sx:{background:"#E600000D",border:`1px solid ${G.palette.custom.borderColor}`,borderRadius:"5px",padding:"5px"},children:[t.jsx(z,{color:"error",sx:{fontSize:"18px"}}),t.jsx(o,{fontSize:"16px",color:"error",marginLeft:.2,fontWeight:"400",children:"Delete"})]})})]})}].map((e=>t.jsxs(n,{sx:{color:"#FFFFFF",padding:0,border:"none"},size:{xs:12,sm:6},children:[t.jsx(o,{fontSize:"14px",fontWeight:"400",sx:{color:G.palette.custom.mainBlue,flex:1,padding:0,border:"none"},children:e.label}),e?.renderCell?e.renderCell():t.jsx(o,{variant:"h6",fontSize:"16px !important",color:"#fff",children:e.value??"--"})]},e.label)))},Ke._id)})})})]},e.serial)))})})})}),t.jsx(Ve,{page:Se,rowsPerPage:Ae,totalRows:le.length,onPageChange:Le,onRowsPerPageChange:qe})]}),t.jsx(se,{showAddKey:i,setShowAddKey:a,setAdding:me,fetchKeys:Te}),t.jsx(ne,{deleteKey:fe,setDeleteKey:ge,setDeleting:ye,fetchKeys:Te}),t.jsx(ie,{revokeKey:we,setRevokeKey:be,setRevoking:Ce,fetchKeys:Te}),ce&&t.jsx(ae,{updateKeyAccess:_e,setUpdateKeyAccess:ke,endpoints:ce,fetchKeys:Te}),t.jsx(oe,{apiKey:Be,showEditModal:!!Be,setShowEditModal:e=>!e&&Me(null),setUpdating:$e,fetchKeys:Te})]})}function de(){const{user:s}=L(),[i,o]=e.useState(""),{isMobile:r}=V(),[l,d]=e.useState(!1),[c,p]=e.useState(""),u=e.useMemo((()=>[{value:"apiKeys",label:"Api Keys",component:t.jsx(le,{showAddKey:l,setShowAddKey:d,searchQuery:c}),display:s?.hasPermissions([U.manageApiKeys])}]),[s,l,c]);return e.useEffect((()=>{i||o(u.find((e=>e.display))?.value||"")}),[u]),s&&u.some((e=>e.display))&&i&&t.jsxs(n,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",overflow:"auto",sx:{backgroundColor:G.palette.custom.darkBlue},children:[t.jsxs(n,{container:!0,padding:2,display:"flex",rowGap:2,justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",children:[t.jsx(n,{size:{xs:12,lg:2.5},children:t.jsx(P,{value:i,onChange:(e,t)=>o(t),sx:{width:"100%",padding:"4px",border:`2px solid ${G.palette.custom.borderColor}`,borderRadius:"8px",backgroundColor:"transparent","& .MuiTabs-flexContainer":{height:"100%"},"& .MuiButtonBase-root":{width:"100%",borderRadius:"8px"},"& .MuiButtonBase-root.Mui-selected":{backgroundColor:G.palette.custom.mainBlue}},children:u.filter((e=>e.display)).map((e=>t.jsx(B,{label:e.label,value:e.value,sx:{maxWidth:"none"}},e.value)))})}),t.jsxs(n,{container:!0,columnGap:2,justifyContent:"space-between",size:{xs:12,lg:9.4},children:[t.jsx(n,{size:{xs:"grow",lg:5.8},children:t.jsx(M,{type:"text",value:c,onChange:e=>{p(e.target.value)},startAdornment:t.jsx(E,{position:"start",children:t.jsx($,{sx:{color:"#FFFFFF"}})}),placeholder:"Search by requests or description",sx:{color:"#FFFFFF",width:"100%","& .MuiOutlinedInput-notchedOutline":{border:"2px solid",borderColor:G.palette.custom.borderColor+" !important",borderRadius:"8px"}}})}),t.jsx(n,{alignItems:"center",display:"flex",justifyContent:"flex-end",gap:2,size:"auto",children:t.jsx(a,{variant:"contained",sx:{"&.MuiButtonBase-root":{color:"#FFFFFF",height:{xs:"100%",lg:"auto"},padding:{xs:0,lg:"10px 20px"},backgroundColor:G.palette.custom.mainBlue,fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},startIcon:t.jsx(x,{}),onClick:()=>d(!0),children:!r&&"Add API Key"})})]})]}),u.filter((e=>e.display)).map((e=>t.jsx(n,{display:i!==e.value&&"none",paddingX:2,paddingBottom:2,width:"100%",size:"grow",children:e.component},e.value)))]})}export{de as default};
