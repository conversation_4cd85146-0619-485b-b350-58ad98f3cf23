import ThingsboardDevices from "../models/ThingsboardDevices";
import microserviceSocket from "../modules/microservice_socket";
import { IThingsboardDevice, IThingsboardService } from "../interfaces/Thingsboard";

class ThingsBoardService implements IThingsboardService {
    async getAllDevices(): Promise<IThingsboardDevice[]> {
        return ThingsboardDevices.find({}, { deviceId: 1, deviceName: 1, accessToken: 1, dashboardId: 1 });
    }

    async getDeviceByUnitId(unitId: string): Promise<IThingsboardDevice | null> {
        return ThingsboardDevices.findOne({ deviceName: unitId }, { deviceId: 1, deviceName: 1, accessToken: 1, dashboardId: 1 });
    }

    resetDashboards() {
        microserviceSocket.emit("thingsboard/reset-dashboard");
    }
}

export default new ThingsBoardService();
