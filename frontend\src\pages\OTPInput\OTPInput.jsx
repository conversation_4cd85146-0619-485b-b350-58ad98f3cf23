import React, { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { TextField, Button, Typography, Grid, CircularProgress } from "@mui/material";
import axiosInstance from "../../axios";
import { useUser } from "../../hooks/UserHook";
import theme from "../../theme";

/*
 * OTP RE-VERIFICATION LOGIC ANALYSIS:
 *
 * The codebase implements a device-based 2FA system with the following behavior:
 *
 * 1. DEVICE COOKIE EXPIRATION: 30 days
 *    - Device IDs are stored in cookies with 30-day expiration (server.ts:47)
 *    - After 30 days, the device cookie expires and a new device ID is generated
 *    - This will trigger OTP verification again for the "new" device
 *
 * 2. JWT TOKEN EXPIRATION: 7 days
 *    - JWT tokens expire after 7 days (User.route.ts:500)
 *    - When JWT expires, user must login again
 *    - If device is still verified (within 30 days), no OTP required
 *    - If device cookie expired, OTP verification required
 *
 * 3. NO PERIODIC RE-VERIFICATION:
 *    - Once a device is verified via OTP, it's added to user.email_verified_device_ids
 *    - This array has NO expiration mechanism - devices stay verified indefinitely
 *    - Only way to require OTP again is:
 *      a) Device cookie expires (30 days)
 *      b) User clears browser cookies
 *      c) User uses a different browser/device
 *      d) Admin manually removes device from user.email_verified_device_ids
 *
 * 4. CURRENT FLOW:
 *    - Login attempt → Check if device in email_verified_device_ids
 *    - If not verified → Return 302 → Redirect to OTP page
 *    - OTP verification → Add device to email_verified_device_ids
 *    - Future logins from same device → No OTP required (until cookie expires)
 */

const OTPInput = () => {
    const [otp, setOtp] = useState(["", "", "", "", "", ""]);
    const [error, setError] = useState("");
    const [submitting, setSubmitting] = useState(false);
    const [timer, setTimer] = useState(0);
    const [isResendDisabled, setIsResendDisabled] = useState(false);
    const [userEmail, setUserEmail] = useState("");
    const location = useLocation();
    const { login } = useUser();
    const navigate = useNavigate();
    const { username, password } = location.state || {};

    const timeoutRef = useRef();

    useEffect(() => {
        if (error) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = setTimeout(() => setError(""), 3000);
        }
    }, [error]);

    const handleChange = (e, index) => {
        const value = e.target.value;
        if (/^[0-9]?$/.test(value)) {
            const newOtp = [...otp];
            newOtp[index] = value;
            setOtp(newOtp);
            if (value && index < otp.length - 1) {
                document.getElementById(`otp-input-${index + 1}`).focus();
            }
        }
    };

    const handlePaste = (e) => {
        const pastedValue = e.clipboardData.getData("text");
        if (/^\d{6}$/.test(pastedValue)) {
            const newOtp = pastedValue.split("");
            setOtp(newOtp);
            newOtp.forEach((_, index) => {
                document.getElementById(`otp-input-${index}`).value = newOtp[index];
            });
        }
        e.preventDefault();
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSubmitting(true);
        const otpCode = otp.join("");
        try {
            if (!username && !password) return setError("Username and password required. Please navigate to login page");
            await axiosInstance.post("/users/emailOTPVerification", { otp: Number(otpCode), username });
            const response = await login({ username, password });
            if (response) {
                const eventPath = sessionStorage.getItem("eventPath");
                if (eventPath) {
                    navigate(eventPath);
                } else {
                    navigate("/dashboard/stream");
                }
            }
            setError("");
        } catch (err) {
            setError(err.response?.data?.message || "An error occurred");
        } finally {
            setSubmitting(false);
        }
    };

    const handleKeyDown = (event, index) => {
        if (event.key === "Backspace" && !otp[index] && index > 0) {
            document.getElementById(`otp-input-${index - 1}`).focus();
        }
    };

    const handleResendCode = async () => {
        try {
            const response = await axiosInstance.post("/users/sendEmailOTP", { username });
            if (response.data.email) {
                setUserEmail(response.data.email);
            }
            setTimer(60);
            setIsResendDisabled(true);
        } catch (error) {
            setError(error.response?.data?.message || "An error occurred");
            console.error(error);
        }
    };

    useEffect(() => {
        if (username) {
            handleResendCode();
        } else {
            navigate("/login");
        }
    }, [username]);

    useEffect(() => {
        if (isResendDisabled) {
            const interval = setInterval(() => {
                setTimer((prevTimer) => {
                    if (prevTimer <= 1) {
                        clearInterval(interval);
                        setIsResendDisabled(false);
                        return 0;
                    }
                    return prevTimer - 1;
                });
            }, 1000);
            return () => clearInterval(interval);
        }
    }, [isResendDisabled]);

    return (
        <Grid container flexDirection="column" gap="32px">
            <Grid container flexDirection="column" color="#FFFFFF">
                <Typography component="h3" fontWeight="600" fontSize="46px" textAlign="center">
                    Enter OTP Code
                </Typography>
                <Typography component="p" fontWeight="500" fontSize="24px">
                    Check your email
                </Typography>
                <Typography component="p" fontWeight="400" fontSize="16px">
                    We&#39;ve sent a 6-digit confirmation OTP code to <strong>{userEmail || username}</strong>. Make sure you enter the correct code.
                </Typography>
            </Grid>

            <Grid container flexDirection="column" component="form" onSubmit={handleSubmit} gap={4}>
                <Grid container justifyContent="center" onPaste={handlePaste} gap="10px">
                    {otp.map((digit, index) => (
                        <TextField
                            key={index}
                            id={`otp-input-${index}`}
                            type="text"
                            value={digit}
                            onChange={(e) => handleChange(e, index)}
                            maxLength="1"
                            variant="outlined"
                            sx={{
                                width: "13%",
                                backgroundColor: "transparent",
                                border: "1px solid #818994",
                                borderRadius: "5px",
                                maxWidth: "80px",
                                minWidth: "30px",
                            }}
                            inputProps={{
                                autoComplete: "off",
                                inputMode: "numeric",
                                pattern: "[0-9]*",
                                "aria-label": `OTP digit ${index + 1}`,
                                style: { color: "#FFFFFF", textAlign: "center", padding: 0, height: "60px" },
                                onKeyDown: (event) => handleKeyDown(event, index),
                            }}
                        />
                    ))}
                </Grid>
                {error && (
                    <Grid>
                        <Typography color="error">{error}</Typography>
                    </Grid>
                )}
                <Grid>
                    <Button
                        type="submit"
                        variant="contained"
                        className="btn-login"
                        fullWidth
                        sx={{
                            color: theme.palette.primary.main,
                            padding: "15px 10px",
                            fontSize: "24px",
                            fontWeight: "600",
                        }}
                        disabled={submitting}
                        endIcon={submitting && <CircularProgress size={20} />}
                    >
                        Confirm OTP
                    </Button>
                </Grid>
                <Grid>
                    <Button
                        variant="text"
                        fullWidth
                        sx={{
                            textDecoration: isResendDisabled ? "none !important" : "underline",
                            color: isResendDisabled ? "#717171 !important" : "#FFFFFF",
                            padding: 0,
                            fontSize: "20px",
                            fontWeight: "500",
                            textTransform: "none",
                        }}
                        onClick={handleResendCode}
                        disabled={isResendDisabled}
                    >
                        {isResendDisabled ? `Resend Code (${Math.floor(timer / 60)}:${String(timer % 60).padStart(2, "0")})` : "Resend Code"}
                    </Button>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default OTPInput;
