import express, { Request, Response, NextFunction } from "express";
import { body } from "express-validator";
import { Types } from "mongoose";
import assignEndpointId from "../../middlewares/assignEndpointId";
import isAuthenticated from "../../middlewares/auth";
import { validateData } from "../../middlewares/validator";
import { validateError, canAccessVessel } from "../../utils/functions";
import { endpointIds } from "../../utils/endpointIds";
import limitPromise from "../../modules/pLimit";
import db from "../../modules/db";
import vesselService from "../../services/Vessel.service";
import rateLimit from "express-rate-limit";
import compression from "compression";
import { IQueryFilter } from "../../interfaces/Common";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 40,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

router.use("/", apiLimiter);
router.use(compression());

router.post(
    "/:vesselId",
    assignEndpointId.bind(this, endpointIds.FETCH_ARTIFACTS_V2),
    isAuthenticated,
    (req: Request, res: Response, next: NextFunction) =>
        validateData(
            [
                // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v: string) => Number(v))
                    .withMessage((value: string, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v: string) => Number(v))
                    .withMessage((value: string, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v: string[]) => v.map((id: string) => new Types.ObjectId(id)))
                    .withMessage((value: string, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselId } = req.params;
            const { startTimestamp, endTimestamp, excludeIds } = req.body;
            console.log(`/v2/artifacts ${vesselId}`, startTimestamp, endTimestamp);

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const vessel = await vesselService.findById({ id: vesselId });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: `Cannot access artifacts for '${vesselId}'` });
            }

            const query: IQueryFilter = { "portal.is_archived": { $ne: true } };
            query.onboard_vessel_id = new Types.ObjectId(vesselId);

            if (startTimestamp) {
                const endTime = endTimestamp || Date.now();
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
            }
            if (excludeIds) query._id = { $nin: excludeIds };
            query.location = { $ne: null };
            query.vessel_presence = true;
            query.super_category = { $ne: null };
            query.host_vessel = { $ne: true };

            const artifacts = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/v2/artifacts ${vesselId} querying DB`);

                const cursor = db.qmai.collection("analysis_results").find(query, {
                    projection: {
                        _id: 1,
                        unit_id: 1,
                        bucket_name: 1,
                        aws_region: 1,
                        image_path: 1,
                        video_path: 1,
                        location: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        others: 1,
                        timestamp: 1,
                        text_extraction: 1,
                        weapons: 1,
                        imo_number: 1,
                        portal: 1,
                        onboard_vessel_id: 1,
                        thumbnail_image_path: 1,
                    },
                });

                if (isSwagger) {
                    cursor.limit(20);
                }

                return await cursor.toArray();
            });
            console.log(`/v2/artifacts ${vesselId} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/v2/artifacts ${vesselId} received ${Array.isArray(artifacts) ? artifacts.length : 0} artifacts`);

            if (isClosed) return res.end();
            res.json({
                artifacts,
            });
            console.log(`/v2/artifacts ${vesselId} time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

/**
 * @swagger
 * tags:
 *   name: Artifacts
 *   description: Fetch vessel artifacts data
 * components:
 *   schemas:
 *     Artifactv2:
 *       type: object
 *       properties:
 *         artifacts:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               _id:
 *                 type: string
 *                 description: Unique identifier for the artifact
 *                 example: "67e537882f2fc5050c31ed73"
 *               unit_id:
 *                 type: string
 *                 description: Unit ID associated with the artifact
 *                 example: "QSX0003"
 *               timestamp:
 *                 type: string
 *                 format: date-time
 *                 description: Timestamp when the artifact was created
 *                 example: "2025-03-31T13:00:00.000Z"
 *               bucket_name:
 *                 type: string
 *                 description: S3 bucket name containing the artifact
 *                 example: "qsx0003"
 *               aws_region:
 *                 type: string
 *                 description: AWS region of the artifact storage
 *                 example: "us-east-1"
 *               image_path:
 *                 type: string
 *                 description: Path to the artifact image in S3
 *                 example: "artifacts/2025-03-27T11:24:24.895Z/image/QSX0003_cam-0_2025-03-27T11:26:19.245Z.jpg"
 *               video_path:
 *                 type: string
 *                 description: Path to the artifact video in S3
 *                 example: "artifacts/2025-03-27T11:24:24.895Z/video/QSX0003_cam-0_2025-03-27T11:26:19.245Z.mp4"
 *               location:
 *                 type: object
 *                 properties:
 *                   type:
 *                     type: string
 *                     example: "Point"
 *                   coordinates:
 *                     type: array
 *                     items:
 *                       type: number
 *                     example: [120.9842, 14.5995]
 *               category:
 *                 type: string
 *                 description: Specific category of the artifact
 *                 example: "Fishing Vessel"
 *               super_category:
 *                 type: string
 *                 description: General category of the artifact
 *                 example: "Fishing"
 *               text_extraction:
 *                 type: array
 *                 description: Extracted text from the artifact
 *                 example: []
 *               imo_number:
 *                 type: string
 *                 nullable: true
 *                 description: IMO number if detected
 *                 example: null
 *               color:
 *                 type: string
 *                 description: Color of the detected object
 *                 example: "Blue and white with green deck"
 *               size:
 *                 type: string
 *                 description: Size classification of the detected object
 *                 example: "medium"
 *               weapons:
 *                 type: string
 *                 nullable: true
 *                 description: Detected weapons information
 *                 example: null
 *               others:
 *                 type: string
 *                 description: Additional metadata
 *                 example: "The vessel has multiple blue barrels and a covered deck area."
 */

/**
 * @swagger
 * /v2/artifacts/{vesselId}:
 *   post:
 *     summary: Fetch artifacts by vessel
 *     description: Retrieves a list of artifacts for a specific vessel within a given time range. Supports filtering by time range, exclusion of specific IDs, and the option to include artifacts with null super categories.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: vesselId
 *         in: path
 *         required: true
 *         description: The ID of the vessel to fetch artifacts for
 *         schema:
 *           type: string
 *           example: 683df473073245cf0fd62be8
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startTimestamp:
 *                 type: number
 *                 description: Start timestamp in milliseconds since epoch
 *                 example: 1686902400000
 *               endTimestamp:
 *                 type: number
 *                 description: End timestamp in milliseconds since epoch
 *                 example: 1686988800000
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of artifact IDs to exclude from the results
 *                 example: ["507f1f77bcf86cd799439011"]
 *     responses:
 *       200:
 *         description: Artifacts data for the vessel
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Artifactv2'
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this unit
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this unit.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */

export default router;
