import mongoose from "mongoose";

export interface IArtifact {
    _id: mongoose.Types.ObjectId | string;
    unit_id: string;
    onboard_vessel_id: mongoose.Types.ObjectId | string;
    onboard_vessel_name?: string;
    portal: null | {
        duplication_index: number;
        archived_by?: mongoose.Types.ObjectId | string;
        is_archived: boolean;
        archived_at?: Date;
    };
    bucket_name: string;
    aws_region: string;
    image_path?: string;
    video_path?: string;
    thumbnail_image_path?: string;
    location: {
        type: string;
        coordinates: number[];
    } | null;
    category?: string;
    super_category: string | null;
    size?: string;
    color?: string;
    others?: string;
    timestamp: Date;
    text_extraction?: Array<{
        text: string;
        confidence: number;
    }>;
    weapons?: string;
    imo_number?: string;
    country_flag?: string;
    vessel_features?: any;
    home_country?: string;
    vessel_orientation?: string;
    host_vessel?: boolean;
    vessel_presence?: boolean;
    video_exists?: boolean;
}

export interface IArtifactFilters {
    start_time?: string;
    end_time?: string;
    categories?: string[];
    id?: string;
    colors?: string[];
    sizes?: string[];
    type?: string;
    vessel_ids?: string[];
    country_flags?: string[];
    weapons?: string[];
    host_vessel?: boolean;
}

export interface IGroupedArtifacts {
    [vesselId: string]: IArtifact[];
}
