import mongoose from "mongoose";
import db from "../modules/db";
import { IHomePort } from "../interfaces/HomePort";

const HomePortSchema = new mongoose.Schema({
    lat: {
        type: Number,
        required: true,
    },
    lng: {
        type: Number,
        required: true,
    },
});

const HomePort = db.qm.model<IHomePort>("HomePort", HomePortSchema, "home_ports");

export default HomePort;
