"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const functions_1 = require("../utils/functions");
const db_1 = __importDefault(require("../modules/db"));
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const pLimit_1 = __importDefault(require("../modules/pLimit"));
const mongoose_1 = __importStar(require("mongoose"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const compression_1 = __importDefault(require("compression"));
const awsS3_1 = require("../modules/awsS3");
const dayjs_1 = __importDefault(require("dayjs"));
const timezonesList_1 = require("../utils/timezonesList");
const Vessel_service_1 = __importDefault(require("../services/Vessel.service"));
const ArtifactFlag_service_1 = __importDefault(require("../services/ArtifactFlag.service"));
const permissions_1 = require("../utils/permissions");
const hasPermission_1 = __importDefault(require("../middlewares/hasPermission"));
const ioEmitter_1 = __importDefault(require("../modules/ioEmitter"));
const Vessel_1 = __importDefault(require("../models/Vessel"));
const router = express_1.default.Router();
router.use((0, compression_1.default)());
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});
const conditionalApiLimiter = (req, res, next) => {
    if (req.path.match(/\/link\//) || req.header("Fetch-From") === "dashboard") {
        // Skip rate limiting for excluded paths
        next();
    }
    else {
        // Apply rate limiting for other paths
        apiLimiter(req, res, next);
    }
};
const capitalize = (str) => str.replace(/\b\w/g, (c) => c.toUpperCase());
const dedupeNormalize = (arr) => {
    const seen = new Set();
    return arr
        .filter((item) => {
        if (!item || typeof item !== "string")
            return false;
        const norm = item.trim().toLowerCase();
        if (seen.has(norm))
            return false;
        seen.add(norm);
        return true;
    })
        .map((item) => capitalize(item.trim().toLowerCase()));
};
const defaultProjection = {
    _id: 1,
    unit_id: 1,
    bucket_name: 1,
    image_path: 1,
    video_path: 1,
    location: 1,
    category: 1,
    super_category: 1,
    size: 1,
    color: 1,
    weapons: 1,
    others: 1,
    timestamp: 1,
    onboard_vessel_name: 1,
    onboard_vessel_id: 1,
    portal: 1,
    country_flag: 1,
    aws_region: 1,
    text_extraction: 1,
    imo_number: 1,
    thumbnail_image_path: 1,
    vessel_features: 1,
    home_country: 1,
    vessel_orientation: 1,
    true_bearing: 1,
    det_nbbox: 1,
};
router.use("/", conditionalApiLimiter);
router.get("/bulk", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ARTIFACTS_BULK), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("vesselIds")
        .isString()
        .withMessage(`vesselIds is a required string`)
        .notEmpty()
        .withMessage(`vesselIds must be a comma-separated string`)
        .if((0, express_validator_1.query)("vesselIds").exists())
        .customSanitizer((v) => v.split(",").map((v) => v.trim()))
        .custom((v) => v.every((id) => (0, mongoose_1.isValidObjectId)(id)))
        .withMessage(`vesselIds must be valid object IDs`),
    (0, express_validator_1.query)("startTimestampISO").isISO8601().withMessage(`startTimestampISO must be a valid ISO 8601 timestamp`).optional(),
    (0, express_validator_1.query)("endTimestampISO").isISO8601().withMessage(`endTimestampISO must be a valid ISO 8601 timestamp`).optional(),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const requestURL = req.get("Referer");
    const isSwagger = requestURL ? requestURL.includes("/docs") : false;
    let isClosed = false;
    const onClose = () => {
        isClosed = true;
    };
    res.on("close", onClose);
    try {
        const ts = new Date().getTime();
        const { vesselIds, startTimestampISO, endTimestampISO } = req.query;
        console.log(`/artifacts/bulk ${vesselIds}`, startTimestampISO, endTimestampISO);
        if (endTimestampISO && !startTimestampISO) {
            return res.status(400).json({ message: "startTimestampISO is required when endTimestampISO is provided" });
        }
        const vessels = yield Vessel_service_1.default.find({ _id: { $in: vesselIds } });
        const assignedVessels = vessels.filter((vessel) => (0, functions_1.canAccessVessel)(req, vessel));
        const query = { "portal.is_archived": { $ne: true } };
        query.onboard_vessel_id = { $in: assignedVessels.map((v) => v._id) };
        if (startTimestampISO) {
            const endTime = endTimestampISO || Date.now();
            query.timestamp = { $gt: new Date(startTimestampISO), $lt: new Date(endTime) };
        }
        query.location = { $ne: null };
        // query.host_vessel = { $ne: true };
        query.vessel_presence = true;
        query.super_category = { $ne: null };
        const artifacts = yield (0, pLimit_1.default)(() => __awaiter(void 0, void 0, void 0, function* () {
            if (isClosed)
                return res.end();
            console.log(`/artifacts/bulk querying DB query: ${JSON.stringify(query)}`);
            const ts = new Date().getTime();
            const cursor = db_1.default.qmai.collection("analysis_results").find(query, {
                projection: {
                    _id: 1,
                    timestamp: 1,
                    location: 1,
                    onboard_vessel_id: 1,
                    video_path: 1,
                    super_category: 1,
                    image_path: 1,
                },
            });
            if (isSwagger) {
                cursor.limit(20);
            }
            const result = (yield cursor.toArray()).map((_a) => {
                var { video_path } = _a, artifact = __rest(_a, ["video_path"]);
                return Object.assign(Object.assign({}, artifact), { video_exists: video_path ? true : false });
            });
            console.log(`/artifacts/bulk time taken to query artifacts ${new Date().getTime() - ts}`);
            // Apply unified logic: group by image_path and create duplications
            const unifyingArtifacts = (0, functions_1.groupByImage)(result);
            console.log("/artifacts/bulk after groupByImage", unifyingArtifacts.length);
            const groupedArtifacts = unifyingArtifacts.reduce((acc, artifact) => {
                const vesselId = artifact.onboard_vessel_id.toString();
                if (!acc[vesselId]) {
                    acc[vesselId] = [];
                }
                acc[vesselId].push(artifact);
                return acc;
            }, {});
            vesselIds.forEach((vesselId) => {
                if (!groupedArtifacts[vesselId]) {
                    groupedArtifacts[vesselId] = [];
                }
            });
            return groupedArtifacts;
        }));
        // console.log(`/artifacts/bulk received ${artifacts.length} artifacts`);
        if (isClosed)
            return res.end();
        res.json({
            artifacts,
        });
        console.log(`/artifacts/bulk time taken to respond ${new Date().getTime() - ts}`);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
    finally {
        res.removeListener("close", onClose);
    }
}));
router.get("/filters", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ARTIFACT_FILTERS), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const countryFlags = yield db_1.default.qm.collection("notification_flags").find().toArray();
        const superCategories = yield db_1.default.qm.collection("notification_categories").find().project({ name: 1, code: 1 }).toArray();
        const sizesRaw = yield db_1.default.qmai.collection("analysis_results").distinct("size", { size: { $ne: null } });
        const weaponsAgg = yield db_1.default.qmai
            .collection("analysis_results")
            .aggregate([
            { $match: { weapons: { $ne: null } } },
            { $group: { _id: "$weapons", count: { $sum: 1 } } },
            { $match: { count: { $gt: 10 } } },
            { $sort: { count: -1 } },
            { $project: { _id: 0, name: "$_id", count: 1 } },
        ])
            .toArray();
        const weapons = weaponsAgg.map((w) => w.name);
        const sizes = dedupeNormalize(sizesRaw);
        const filterItems = {
            superCategories: superCategories.map((itm) => itm.name),
            countryFlags: countryFlags.map((itm) => {
                return { name: itm.name, code: itm.code };
            }),
            sizes,
            weapons,
        };
        res.status(200).json(filterItems);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/:vesselName", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ARTIFACTS), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    // param('vesselName').isString().notEmpty().withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("startTimestamp")
        .isInt()
        .customSanitizer((v) => Number(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("endTimestamp")
        .isInt()
        .customSanitizer((v) => Number(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("excludeIds")
        .isArray()
        .bail()
        .customSanitizer((v) => v.map((id) => new mongoose_1.default.Types.ObjectId(id)))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const requestURL = req.get("Referer");
    const isSwagger = requestURL ? requestURL.includes("/docs") : false;
    let isClosed = false;
    const onClose = () => {
        isClosed = true;
    };
    res.on("close", onClose);
    try {
        const ts = new Date().getTime();
        const { vesselName } = req.params;
        const { startTimestamp, endTimestamp, excludeIds } = req.body;
        console.log(`/artifacts ${vesselName}`, startTimestamp, endTimestamp);
        if (endTimestamp && !startTimestamp) {
            return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
        }
        const vessel = yield Vessel_service_1.default.findByAssignedUnitId({ unitId: vesselName });
        if (!vessel)
            return res.status(404).json({ message: "Unit does not exist or is no longer active" });
        if (!(0, functions_1.canAccessVessel)(req, vessel)) {
            return res.status(403).json({ message: `Cannot access artifacts for '${vesselName}'` });
        }
        const query = { "portal.is_archived": { $ne: true } };
        query.onboard_vessel_id = vessel._id;
        // query.host_vessel = { $ne: true };
        query.location = { $ne: null };
        query.vessel_presence = true;
        query.super_category = { $ne: null };
        if (startTimestamp) {
            query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTimestamp || Date.now()) };
        }
        if (excludeIds)
            query._id = { $nin: excludeIds };
        const artifacts = yield (0, pLimit_1.default)(() => __awaiter(void 0, void 0, void 0, function* () {
            if (isClosed)
                return res.end();
            console.log(`/artifacts ${vesselName} querying DB`);
            const cursor = db_1.default.qmai.collection("analysis_results").find(query, {
                projection: {
                    _id: 1,
                    unit_id: 1,
                    onboard_vessel_id: 1,
                    portal: 1,
                    bucket_name: 1,
                    aws_region: 1,
                    image_path: 1,
                    video_path: 1,
                    location: 1,
                    category: 1,
                    super_category: 1,
                    size: 1,
                    color: 1,
                    others: 1,
                    timestamp: 1,
                    text_extraction: 1,
                    weapons: 1,
                    imo_number: 1,
                    thumbnail_image_path: 1,
                    true_bearing: 1,
                    det_nbbox: 1,
                },
            });
            if (isSwagger) {
                cursor.limit(20);
            }
            return yield cursor.toArray();
        }));
        console.log(`/artifacts ${vesselName} time taken to query ${new Date().getTime() - ts}`);
        console.log(`/artifacts ${vesselName} received ${Array.isArray(artifacts) && artifacts.length} artifacts`);
        if (isClosed)
            return res.end();
        res.json(artifacts);
        console.log(`/artifacts ${vesselName} time taken to respond ${new Date().getTime() - ts}`);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
    finally {
        res.removeListener("close", onClose);
    }
}));
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_PAGINATED_ARTIFACTS), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.body)("excludeIds")
        .isArray()
        .bail()
        .customSanitizer((v) => v.map((id) => new mongoose_1.default.Types.ObjectId(id)))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("page").isInt({ min: 1 }).default(1).withMessage("Page must be a positive integer"),
    (0, express_validator_1.body)("pageSize").isInt({ min: 1 }).default(20).withMessage("Page size must be a positive integer"),
    (0, express_validator_1.body)("filters").isObject().withMessage("Filters must be a valid JSON object").optional(),
    (0, express_validator_1.body)("group")
        .isInt({ min: 0, max: 1 })
        .optional()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("projection")
        .isObject()
        .optional()
        .withMessage("Projection must be a valid JSON object")
        .custom((projection) => {
        if (!projection)
            return true; // Allow empty projection
        const allowedFields = Object.entries(defaultProjection)
            .map(([key, value]) => (value ? key : null))
            .filter(Boolean);
        const projectionKeys = Object.keys(projection);
        const invalidKeys = projectionKeys.filter((key) => !allowedFields.includes(key));
        if (invalidKeys.length > 0) {
            throw new Error(`Invalid projection fields: ${invalidKeys.join(", ")}. Allowed fields: ${allowedFields.join(", ")}`);
        }
        return true;
    }),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let isClosed = false;
    const onClose = () => {
        isClosed = true;
    };
    res.on("close", onClose);
    try {
        const ts = new Date().getTime();
        const { excludeIds, page, pageSize, filters, group, projection } = req.body;
        console.log(`/artifacts`);
        const query = { "portal.is_archived": { $ne: true } };
        if (excludeIds)
            query._id = { $nin: excludeIds };
        query.location = { $ne: null };
        query.video_path = { $exists: false };
        query.vessel_presence = true;
        query.super_category = { $ne: null };
        let allowedVessels = [];
        const allVessels = yield Vessel_service_1.default.find({}, { _id: 1, is_active: 1, region_group_id: 1 });
        allowedVessels = allVessels.filter((v) => (0, functions_1.canAccessVessel)(req, v)).map((v) => v._id.toString());
        query.onboard_vessel_id = { $in: allowedVessels.map((v) => new mongoose_1.default.Types.ObjectId(v)) };
        // console.log("allowedVessels", allowedVessels);
        if (filters) {
            const { start_time, end_time, categories, id, colors, sizes, type, vessel_ids, country_flags, weapons, host_vessel } = filters;
            if (id) {
                query._id = new mongoose_1.default.Types.ObjectId(id);
            }
            else {
                if (start_time && end_time)
                    query.timestamp = { $gt: new Date(start_time), $lt: new Date(end_time) };
                if (categories)
                    query.super_category = { $in: categories };
                if (colors && Array.isArray(colors) && colors.length > 0) {
                    query.$and = colors.map((color) => ({
                        color: { $regex: new RegExp(color, "i") },
                    }));
                }
                if (sizes && Array.isArray(sizes) && sizes.length > 0) {
                    query.$or = sizes.map((size) => ({
                        size: { $regex: new RegExp(`^${size}$`, "i") },
                    }));
                }
                if (vessel_ids) {
                    const filteredVesselIds = vessel_ids.filter((v) => allowedVessels.includes(v));
                    if (filteredVesselIds.length === 0) {
                        return res.status(403).json({ message: `Cannot access artifacts for '${vessel_ids}'` });
                    }
                    query.onboard_vessel_id = { $in: filteredVesselIds.map((v) => new mongoose_1.default.Types.ObjectId(v)) };
                }
                if (country_flags)
                    query.country_flag = { $in: country_flags };
                if (weapons && Array.isArray(weapons) && weapons.length > 0) {
                    query.weapons = { $in: weapons };
                }
            }
            if (type) {
                if (type === "video") {
                    query.video_path = { $exists: true, $ne: null };
                }
                else if (type === "image") {
                    query.video_path = { $exists: false };
                }
            }
            if (host_vessel === true) {
                query.host_vessel = true;
            }
            else if (host_vessel === false) {
                query.host_vessel = { $ne: true };
            }
        }
        const skip = (page - 1) * pageSize;
        const limit = pageSize;
        const totalCount = yield db_1.default.qmai.collection("analysis_results").countDocuments(query);
        const artifacts = yield (0, pLimit_1.default)(() => __awaiter(void 0, void 0, void 0, function* () {
            if (isClosed)
                return res.end();
            console.log(`/artifacts querying DB`);
            const rawArtifacts = yield db_1.default.qmai
                .collection("analysis_results")
                .find(query)
                .sort({ timestamp: -1 }) // Sort by timestamp in descending order
                .project(projection || defaultProjection)
                .skip(skip)
                .limit(limit)
                .toArray();
            // Add signed URLs for media
            return rawArtifacts.map((artifact) => {
                const artifactWithUrls = Object.assign({}, artifact);
                if (artifact.image_path) {
                    artifactWithUrls.image_url = (0, awsS3_1.processBatchItem)({
                        bucketName: artifact.bucket_name,
                        key: artifact.image_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                if (artifact.video_path) {
                    artifactWithUrls.video_url = (0, awsS3_1.processBatchItem)({
                        bucketName: artifact.bucket_name,
                        key: artifact.video_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                if (artifact.thumbnail_image_path) {
                    artifactWithUrls.thumbnail_url = (0, awsS3_1.processBatchItem)({
                        bucketName: awsS3_1.s3Config.buckets.compressedItems.name,
                        key: artifact.thumbnail_image_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                return artifactWithUrls;
            });
        }));
        console.log(`/artifacts time taken to query ${new Date().getTime() - ts}`);
        console.log(`/artifacts received ${Array.isArray(artifacts) && artifacts.length} artifacts`);
        if (isClosed)
            return res.end();
        // filter the unique artifacts based on image_path
        const unifyingArtifacts = (0, functions_1.groupByImage)(artifacts);
        console.log("/artifacts after  groupByImage", unifyingArtifacts.length);
        let groupedArtifacts;
        if (group && Array.isArray(unifyingArtifacts) && unifyingArtifacts.length > 0) {
            groupedArtifacts = (0, functions_1.groupArtifactsByDuplicateIndex)(unifyingArtifacts, 0.7);
            console.log(`/artifacts grouped ${Array.isArray(groupedArtifacts) && groupedArtifacts.length} groups from ${Array.isArray(artifacts) && artifacts.length} artifacts`);
        }
        const data = Object.assign({ artifacts: unifyingArtifacts, page,
            pageSize,
            totalCount }, (groupedArtifacts && { groupedArtifacts }));
        res.json(data);
        console.log(`/artifacts time taken to respond ${new Date().getTime() - ts}`);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
    finally {
        res.removeListener("close", onClose);
    }
}));
router.get("/detail/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ARTIFACT_DETAIL), auth_1.default, validator_1.validateData.bind(this, [(0, express_validator_1.param)("id").isMongoId().withMessage("Invalid artifact ID")]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const artifact = yield db_1.default.qmai.collection("analysis_results").findOne({ _id: new mongoose_1.default.Types.ObjectId(id) }, {
            projection: {
                _id: 1,
                unit_id: 1,
                bucket_name: 1,
                aws_region: 1,
                image_path: 1,
                video_path: 1,
                thumbnail_image_path: 1,
                location: 1,
                category: 1,
                super_category: 1,
                size: 1,
                color: 1,
                others: 1,
                timestamp: 1,
                text_extraction: 1,
                weapons: 1,
                imo_number: 1,
                onboard_vessel_id: 1,
                onboard_vessel_name: 1,
                portal: 1,
                country_flag: 1,
                vessel_features: 1,
                home_country: 1,
                vessel_orientation: 1,
                true_bearing: 1,
                det_nbbox: 1,
            },
        });
        if (!artifact) {
            return res.status(404).json({ message: "Artifact not found" });
        }
        if (req.user && !(0, functions_1.userHasPermissions)(req.user, [permissions_1.permissions.accessAllVessels])) {
            if (!artifact.onboard_vessel_id) {
                return res.status(403).json({ message: "Cannot access this artifact" });
            }
            const vessel = yield Vessel_1.default.findById(artifact.onboard_vessel_id);
            if (!vessel || !(0, functions_1.canAccessVessel)(req, vessel)) {
                return res.status(403).json({ message: "Cannot access this vessel artifact" });
            }
        }
        if (artifact.image_path) {
            artifact.image_url = (0, awsS3_1.processBatchItem)({
                bucketName: artifact.bucket_name,
                key: artifact.image_path,
                region: artifact.aws_region,
            }).signedUrl;
        }
        if (artifact.video_path) {
            artifact.video_url = (0, awsS3_1.processBatchItem)({
                bucketName: artifact.bucket_name,
                key: artifact.video_path,
                region: artifact.aws_region,
            }).signedUrl;
        }
        if (artifact.thumbnail_image_path) {
            artifact.thumbnail_url = (0, awsS3_1.processBatchItem)({
                bucketName: awsS3_1.s3Config.buckets.compressedItems.name,
                key: artifact.thumbnail_image_path,
                region: artifact.aws_region,
            }).signedUrl;
        }
        res.json(artifact);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/:id/download", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.DOWNLOAD_ARTIFACT), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const artifact = yield db_1.default.qmai.collection("analysis_results").findOne({ _id: new mongoose_1.default.Types.ObjectId(id) });
        if (!artifact) {
            return res.status(404).json({ message: "Artifact not found" });
        }
        const getMetadata = () => {
            var _a, _b;
            return `
                Timestamp: ${(0, dayjs_1.default)(artifact.timestamp).format(req.user.date_time_format || timezonesList_1.defaultDateTimeFormat)} 
                Category: ${artifact.super_category}
                Sub Category: ${artifact.category}
                Color: ${artifact.color}
                Size: ${artifact.size}
                Location: ${(((_a = artifact.location) === null || _a === void 0 ? void 0 : _a.coordinates) || [])[0]}, ${(((_b = artifact.location) === null || _b === void 0 ? void 0 : _b.coordinates) || [])[1]}
                Others: ${artifact.others}
            `;
        };
        const filenameMask = `${(0, functions_1.fileNameTimestamp)()} - ${(artifact.onboard_vessel_name || artifact.unit_id).replace(/ /g, "_")}`;
        const mediaKey = artifact.video_path || artifact.image_path;
        const mediaPathParts = mediaKey.split(".");
        const extension = mediaPathParts[mediaPathParts.length - 1];
        const mediaData = yield (0, awsS3_1.getObjectStream)(artifact.bucket_name, artifact.aws_region, mediaKey);
        const filesData = [
            {
                name: `${filenameMask}.${extension}`,
                content: mediaData,
            },
            {
                name: `${filenameMask}.txt`,
                content: getMetadata(),
            },
        ];
        const zip = (0, functions_1.generateZip)(filesData);
        const stream = zip.generateNodeStream({ type: "nodebuffer", streamFiles: true });
        //Using pipeline for better error handling.
        res.setHeader("Content-Type", "application/zip");
        res.setHeader("Content-Disposition", `attachment; filename="${filenameMask}.zip"`);
        yield new Promise((resolve, reject) => {
            mediaData.on("error", (err) => {
                console.error("S3 stream error:", err);
                reject(err);
            });
            stream
                .pipe(res)
                .on("finish", resolve)
                .on("error", (e) => {
                reject(e);
            });
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/hoursAggregatedCount", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_HOURS_AGGREGATED_COUNT), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("startTimestamp").isInt().toInt().withMessage("Invalid startTimestamp"),
    (0, express_validator_1.query)("endTimestamp").isInt().toInt().withMessage("Invalid endTimestamp"),
    (0, express_validator_1.query)("interval").isInt({ min: 1 }).toInt().withMessage("Interval must be a positive integer"),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { startTimestamp, endTimestamp, interval } = req.query;
        const startDate = new Date(Number(startTimestamp));
        const endDate = new Date(Number(endTimestamp));
        const aggregatedData = yield db_1.default.qmai
            .collection("analysis_results")
            .aggregate([
            { $match: { timestamp: { $gte: startDate, $lt: endDate }, vessel_presence: true, super_category: { $ne: null } } },
            {
                $group: {
                    _id: {
                        $dateTrunc: {
                            date: "$timestamp",
                            unit: "minute",
                            binSize: interval,
                        },
                    },
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    timestamp: "$_id",
                    count: 1,
                    _id: 0,
                },
            },
            { $sort: { timestamp: 1 } },
        ])
            .toArray();
        const statistics = aggregatedData.reduce((acc, entry) => {
            acc[entry.timestamp.toISOString()] = entry.count;
            return acc;
        }, {});
        const concatenatedTimeSeries = Object.assign(Object.assign({}, (0, functions_1.generateTimeSeries)(Number(startTimestamp), Number(endTimestamp), Number(interval))), statistics);
        const convertedToArray = Object.entries(concatenatedTimeSeries)
            .map(([key, value]) => ({
            timestamp: key,
            count: value,
        }))
            .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        res.status(200).json(convertedToArray);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/:id/archive", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.ARCHIVE_ARTIFACT), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageArtifacts]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const userId = req.user._id;
        const result = yield db_1.default.qmai.collection("analysis_results").findOneAndUpdate({ _id: new mongoose_1.default.Types.ObjectId(id) }, {
            $set: {
                portal: {
                    is_archived: true,
                    archived_by: userId,
                    archived_at: new Date(),
                },
            },
        }, { returnDocument: "after" });
        if (!result.value)
            return res.status(404).json({ message: "Artifact not found" });
        ioEmitter_1.default.emit("notifyAll", {
            name: "artifact/changed",
            data: { artifact: result.value, action: "archived" },
        });
        res.json({ message: "Artifact archived successfully", artifact: result.value });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/:id/unarchive", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UNARCHIVE_ARTIFACT), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageArtifacts]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const result = yield db_1.default.qmai.collection("analysis_results").findOneAndUpdate({ _id: new mongoose_1.default.Types.ObjectId(id) }, {
            $set: {
                portal: {
                    is_archived: false,
                    archived_by: null,
                    archived_at: null,
                },
            },
        }, { returnDocument: "after" });
        if (!result.value)
            return res.status(404).json({ message: "Artifact not found" });
        ioEmitter_1.default.emit("notifyAll", {
            name: "artifact/changed",
            data: { artifact: result.value, action: "unarchived" },
        });
        res.json({ message: "Artifact unarchived successfully", artifact: result.value });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/archived", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ARCHIVED_ARTIFACTS), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageArtifacts]), (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("page").isInt({ min: 1 }).toInt().withMessage("Page must be a positive integer"),
    (0, express_validator_1.query)("pageSize").isInt({ min: 1 }).toInt().withMessage("Page size must be a positive integer"),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 100 } = req.query;
        console.log(`/artifacts/archived`);
        const query = { "portal.is_archived": true, vessel_presence: true, super_category: { $ne: null } };
        const skip = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        const totalCount = yield db_1.default.qmai.collection("analysis_results").countDocuments(query);
        const artifacts = yield db_1.default.qmai
            .collection("analysis_results")
            .find(query)
            .sort({ "portal.archived_at": -1 })
            .project({
            _id: 1,
            unit_id: 1,
            bucket_name: 1,
            image_path: 1,
            video_path: 1,
            thumbnail_image_path: 1,
            location: 1,
            category: 1,
            super_category: 1,
            size: 1,
            color: 1,
            weapons: 1,
            others: 1,
            timestamp: 1,
            onboard_vessel_name: 1,
            onboard_vessel_id: 1,
            portal: 1,
            country_flag: 1,
            aws_region: 1,
            text_extraction: 1,
            imo_number: 1,
            true_bearing: 1,
            det_nbbox: 1,
        })
            .skip(skip)
            .limit(limit)
            .toArray();
        const artifactsWithUrls = (artifacts || []).map((artifact) => {
            const withUrls = Object.assign({}, artifact);
            if (artifact === null || artifact === void 0 ? void 0 : artifact.image_path) {
                withUrls.image_url = (0, awsS3_1.processBatchItem)({
                    bucketName: artifact.bucket_name,
                    key: artifact.image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            if (artifact === null || artifact === void 0 ? void 0 : artifact.video_path) {
                withUrls.video_url = (0, awsS3_1.processBatchItem)({
                    bucketName: artifact.bucket_name,
                    key: artifact.video_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            if (artifact === null || artifact === void 0 ? void 0 : artifact.thumbnail_image_path) {
                withUrls.thumbnail_url = (0, awsS3_1.processBatchItem)({
                    bucketName: awsS3_1.s3Config.buckets.compressedItems.name,
                    key: artifact.thumbnail_image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            return withUrls;
        });
        console.log(`/artifacts/archived received ${artifactsWithUrls === null || artifactsWithUrls === void 0 ? void 0 : artifactsWithUrls.length} artifacts`);
        res.json({
            artifacts: artifactsWithUrls,
            page,
            pageSize,
            totalCount,
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
//This route is used for fetching the artifact indicator activity
router.post("/activityIndicators/bulk", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ARTIFACT_INDICATORS), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.body)("vesselIds")
        .isArray({ min: 1 })
        .customSanitizer((v) => v.map((id) => new mongoose_1.default.Types.ObjectId(id)))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("startTimestamp")
        .isInt()
        .customSanitizer((v) => Number(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("endTimestamp")
        .isInt()
        .customSanitizer((v) => Number(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { vesselIds, startTimestamp, endTimestamp } = req.body;
        if (!endTimestamp && !startTimestamp) {
            return res.status(400).json({ message: "startTimestamp & endTimestamp is required" });
        }
        // Only allow vesselIds that user can access
        const allowedVesselIds = [];
        for (const vesselId of vesselIds) {
            // Optionally, you can fetch vessel and check access
            const vessel = yield Vessel_service_1.default.findById({ id: vesselId });
            if (vessel && (0, functions_1.canAccessVessel)(req, vessel)) {
                allowedVesselIds.push(vesselId);
            }
        }
        if (allowedVesselIds.length === 0) {
            return res.status(403).json({ message: "No accessible vessels" });
        }
        // Query all artifacts for allowed vessels in one go
        const aggregationPipeline = [
            {
                $match: {
                    onboard_vessel_id: { $in: allowedVesselIds },
                    "portal.is_archived": { $ne: true },
                    location: { $ne: null },
                    vessel_presence: true,
                    super_category: { $ne: null },
                    timestamp: {
                        $gt: new Date(startTimestamp),
                        $lt: new Date(endTimestamp),
                    },
                },
            },
            {
                $project: {
                    _id: { $toString: "$_id" },
                    onboard_vessel_id: { $toString: "$onboard_vessel_id" },
                },
            },
            {
                $group: {
                    _id: "$onboard_vessel_id",
                    artifacts: { $push: { _id: "$_id", onboard_vessel_id: "$onboard_vessel_id" } },
                },
            },
        ];
        const groupedResults = yield db_1.default.qmai.collection("analysis_results").aggregate(aggregationPipeline).toArray();
        res.json({
            latestArtifacts: groupedResults,
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/:id/flag", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FLAG_ARTIFACT), auth_1.default, validator_1.validateData.bind(this, [(0, express_validator_1.param)("id").isMongoId().withMessage("Invalid artifact ID")]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const userId = req.user._id;
        const flag = yield ArtifactFlag_service_1.default.flagArtifact(id, userId);
        res.json({ message: "Artifact flagged successfully", flag });
    }
    catch (err) {
        if (err.message === "Artifact not found") {
            return res.status(404).json({ message: err.message });
        }
        if (err.message === "You have already flagged this artifact") {
            return res.status(400).json({ message: err.message });
        }
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/:id/unflag", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UNFLAG_ARTIFACT), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const userId = req.user._id;
        const flag = yield ArtifactFlag_service_1.default.unflagArtifact(id, userId);
        res.json({ message: "Artifact unflagged successfully", flag });
    }
    catch (err) {
        if (err.message === "Flag not found") {
            return res.status(404).json({ message: err.message });
        }
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/flagged", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_FLAGGED_ARTIFACTS), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageArtifacts]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const artifacts = yield ArtifactFlag_service_1.default.getFlaggedArtifacts();
        res.json(artifacts);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/flagged/user", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_USER_FLAGGED_ARTIFACT_IDS), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user._id;
        const artifactIds = yield ArtifactFlag_service_1.default.getUserFlaggedArtifactIds(userId);
        res.json({ flaggedArtifactIds: artifactIds });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.delete("/:id/flags", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.REMOVE_ALL_FLAGS_FROM_ARTIFACT), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageArtifacts]), validator_1.validateData.bind(this, [(0, express_validator_1.param)("id").isMongoId().withMessage("Invalid artifact ID")]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const result = yield ArtifactFlag_service_1.default.removeAllFlagsFromArtifact(id);
        res.json({
            message: "All flags removed from artifact successfully",
            deletedCount: result.deletedCount,
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
/**
 * @swagger
 * tags:
 *   name: Artifacts
 *   description: Fetch vessel artifacts data
 * components:
 *   schemas:
 *     Artifact:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document id of the artifact.
 *           example: "66ee885da6db303e08c075cb"
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the artifact was recorded.
 *           example: "2024-09-21T08:41:43.736Z"
 *         bucket_name:
 *           type: string
 *           description: Name of the S3 bucket where the artifact is stored.
 *           example: "smartmast-prototype-33-ap"
 *         unit_id:
 *           type: string
 *           description: Identifier for the vessel.
 *           example: "prototype-33"
 *         image_path:
 *           type: string
 *           description: Path to the artifact image in the S3 bucket.
 *           example: "artifacts/2024-09-21T07:22:34.941Z/image/prototype-33_cam-1_2024-09-21T08:41:43.736Z.jpg"
 *         location:
 *           type: object
 *           nullable: true
 *           properties:
 *             type:
 *               type: string
 *               description: Type of the location.
 *               example: "Point"
 *             coordinates:
 *               type: array
 *               items:
 *                 type: number
 *               description: Coordinates of the location.
 *               example: [12.5, 117.2075473]
 *         category:
 *           type: string
 *           nullable: true
 *           description: Category of the object in the artifact.
 *           example: "Banca"
 *         super_category:
 *           type: string
 *           nullable: true
 *           description: Broad classification of the object.
 *           example: "Fishing"
 *         color:
 *           type: string
 *           nullable: true
 *           description: Color description of the object in the artifact.
 *           example: "Gray and white"
 *         size:
 *           type: string
 *           nullable: true
 *           description: Size of the object.
 *           example: "Small"
 *         text_extraction:
 *           type: array
 *           description: Text extraction from the artifact.
 *           items:
 *             type: object
 *             properties:
 *               text:
 *                 type: string
 *                 description: Text extracted from the artifact.
 *                 example: "Traditional outrigger design, likely used for small-scale fishing."
 *               confidence:
 *                 type: number
 *                 description: Confidence score of the text extraction.
 *                 example: 0.95
 *         others:
 *           type: string
 *           nullable: true
 *           description: Additional information or description of the object.
 *           example: "Traditional outrigger design, likely used for small-scale fishing."
 *         aws_region:
 *           type: string
 *           nullable: true
 *           description: AWS region where the artifact is stored.
 *           example: "ap-southeast-1"
 *         video_path:
 *           type: string
 *           nullable: true
 *           description: Path to the artifact video in the S3 bucket.
 *           example: "artifacts/2024-09-21T07:22:34.941Z/video/prototype-33_cam-1_2024-09-21T08:41:43.736Z.mp4"
 *         imo_number:
 *           type: string
 *           nullable: true
 *           description: IMO number of the vessel.
 *           example: "9767890"
 *         weapons:
 *           type: string
 *           nullable: true
 *           description: Weapon of the artifact.
 *           example: "Missile"
 *         onboard_vessel_id:
 *           type: string
 *           nullable: true
 *           description: ID of the onboard vessel.
 *           example: "683df46b073245cf0fd62bb9"
 *         image_url:
 *           type: string
 *           nullable: true
 *           description: Signed URL for the artifact image.
 *           example: "https://s3.amazonaws.com/bucket/image.jpg?X-Amz-Algorithm=..."
 *         video_url:
 *           type: string
 *           nullable: true
 *           description: Signed URL for the artifact video.
 *           example: "https://s3.amazonaws.com/bucket/video.mp4?X-Amz-Algorithm=..."
 *         thumbnail_url:
 *           type: string
 *           nullable: true
 *           description: Signed URL for the artifact thumbnail.
 *           example: "https://s3.amazonaws.com/bucket/thumbnail.jpg?X-Amz-Algorithm=..."
 */
/**
 * @swagger
 * /artifacts/filters:
 *   get:
 *     summary: Fetch available artifact filters
 *     description: Retrieves distinct values for artifact filter fields, such as categories, country flags, onboard vessel names, and super categories.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: A list of available filter options.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 countryFlags:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         description: Name of the country flag.
 *                         example: "Alaska"
 *                       code:
 *                         type: string
 *                         description: Code of the country flag.
 *                         example: "AL"
 *                   description: List of distinct country flags.
 *                   example: [{"name": "Alaska", "code": "AL"}, {"name": "Argentina", "code": "AR"}]
 *                 superCategories:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct super categories.
 *                   example: ["Cargo", "Fishing"]
 *                 sizes:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct sizes.
 *                   example: ["Small", "Medium", "Large"]
 *                 colors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct colors.
 *                   example: ["Red", "Green", "Blue"]
 *                 weapons:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         description: Name of the weapon.
 *                         example: "Missile"
 *                       count:
 *                         type: integer
 *                         description: Frequency count of the weapon.
 *                         example: 15
 *                   description: List of distinct weapons with frequency > 10.
 *                   example: [{"name": "Missile", "count": 15}, {"name": "Gun", "count": 12}]
 *       401:
 *         description: Unauthorized. User must be authenticated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Authentication error message.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */
/**
 * @swagger
 * /artifacts:
 *   post:
 *     summary: Fetch paginated artifacts
 *     description: Retrieves a paginated list of artifacts from the database based on applied filters. Supports filtering by time range, category, and exclusion of specific IDs.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: A list of artifact IDs to exclude from the results.
 *                 example: ["66ef4f6ea6db303e08c0767c", "66ef4f95a6db303e08c0767d"]
 *               page:
 *                 type: integer
 *                 description: The page number for pagination (must be a positive integer).
 *                 example: 1
 *               pageSize:
 *                 type: integer
 *                 description: The number of results per page (must be a positive integer).
 *                 example: 20
 *               filters:
 *                 type: object
 *                 description: Additional filters to apply to the query.
 *                 properties:
 *                   start_time:
 *                     type: string
 *                     format: date-time
 *                     description: Start timestamp for filtering artifacts.
 *                     example: "2024-09-21T08:41:43.736Z"
 *                   end_time:
 *                     type: string
 *                     format: date-time
 *                     description: End timestamp for filtering artifacts.
 *                     example: "2024-09-22T08:41:43.736Z"
 *                   categories:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of super categories to filter artifacts.
 *                     example: ["Banca", "Trawler"]
 *                   id:
 *                     type: string
 *                     description: Filter by a specific artifact ID.
 *                     example: "66ef4f6ea6db303e08c0767c"
 *                   colors:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of colors to filter artifacts.
 *                     example: ["Red", "Blue"]
 *                   sizes:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of sizes to filter artifacts.
 *                     example: ["Small", "Large"]
 *                   type:
 *                     type: string
 *                     description: Type of artifact to filter ("video" or "image").
 *                     example: "image"
 *                   vessel_ids:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of vessel IDs to filter artifacts.
 *                     example: ["683df46b073245cf0fd62bb9"]
 *                   country_flags:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of country flags to filter artifacts.
 *                     example: ["Alaska"]
 *                   weapons:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of weapons to filter artifacts.
 *                     example: ["Missile", "Gun"]
 *               favourites:
 *                 type: integer
 *                 description: Whether to include favourite artifacts data (0 or 1).
 *                 example: 1
 *               group:
 *                 type: integer
 *                 description: Whether to apply artifact grouping based on duplication_index (0 or 1).
 *                 example: 1
 *
 *     responses:
 *       200:
 *         description: A list of paginated artifacts.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 artifacts:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Artifact'
 *                 page:
 *                   type: integer
 *                   description: Current page number.
 *                   example: 1
 *                 pageSize:
 *                   type: integer
 *                   description: Number of results per page.
 *                   example: 20
 *                 totalCount:
 *                   type: integer
 *                   description: Total number of artifacts matching the query.
 *                   example: 100
 *                 groupedArtifacts:
 *                   type: array
 *                   description: Grouped artifact IDs (only included when group=1).
 *                   items:
 *                     type: array
 *                     items:
 *                       type: string
 *                   example: [["artifact1", "artifact2"], ["artifact3", "artifact4"]]
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this unit
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this unit.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */
/**
 * @swagger
 * /artifacts:
 *   post:
 *     summary: Fetch paginated artifacts
 *     description: Retrieves a paginated list of artifacts from the database based on applied filters. Supports filtering by time range, category, and exclusion of specific IDs.
 *     tags: [Artifacts]
 *     deprecated: true
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: A list of artifact IDs to exclude from the results.
 *                 example: ["66ef4f6ea6db303e08c0767c", "66ef4f95a6db303e08c0767d"]
 *               page:
 *                 type: integer
 *                 description: The page number for pagination (must be a positive integer).
 *                 example: 1
 *               pageSize:
 *                 type: integer
 *                 description: The number of results per page (must be a positive integer).
 *                 example: 20
 *               filters:
 *                 type: object
 *                 description: Additional filters to apply to the query.
 *                 properties:
 *                   start_time:
 *                     type: string
 *                     format: date-time
 *                     description: Start timestamp for filtering artifacts.
 *                     example: "2024-09-21T08:41:43.736Z"
 *                   end_time:
 *                     type: string
 *                     format: date-time
 *                     description: End timestamp for filtering artifacts.
 *                     example: "2024-09-22T08:41:43.736Z"
 *                   categories:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of super categories to filter artifacts.
 *                     example: ["Banca", "Trawler"]
 *                   id:
 *                     type: string
 *                     description: Filter by a specific artifact ID.
 *                     example: "66ef4f6ea6db303e08c0767c"
 *                   colors:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of colors to filter artifacts.
 *                     example: ["Red", "Blue"]
 *                   sizes:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of sizes to filter artifacts.
 *                     example: ["Small", "Large"]
 *                   type:
 *                     type: string
 *                     description: Type of artifact to filter ("video" or "image").
 *                     example: "image"
 *                   vessel_ids:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of vessel IDs to filter artifacts.
 *                     example: ["683df46b073245cf0fd62bb9"]
 *                   country_flags:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of country flags to filter artifacts.
 *                     example: ["Alaska"]
 *                   weapons:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of weapons to filter artifacts.
 *                     example: ["Missile", "Gun"]
 *               favourites:
 *                 type: integer
 *                 description: Whether to include favourite artifacts data (0 or 1).
 *                 example: 1
 *               group:
 *                 type: integer
 *                 description: Whether to apply artifact grouping based on duplication_index (0 or 1).
 *                 example: 1
 *
 *     responses:
 *       200:
 *         description: A list of paginated artifacts.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 artifacts:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Artifact'
 *                 page:
 *                   type: integer
 *                   description: Current page number.
 *                   example: 1
 *                 pageSize:
 *                   type: integer
 *                   description: Number of results per page.
 *                   example: 20
 *                 totalCount:
 *                   type: integer
 *                   description: Total number of artifacts matching the query.
 *                   example: 100
 *                 groupedArtifacts:
 *                   type: array
 *                   description: Grouped artifact IDs (only included when group=1).
 *                   items:
 *                     type: array
 *                     items:
 *                       type: string
 *                   example: [["artifact1", "artifact2"], ["artifact3", "artifact4"]]
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this unit
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this unit.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */
/**
 * @swagger
 * /artifacts/{vesselName}:
 *   post:
 *     summary: Fetch artifacts by vessel. (This route is deprecated, use v2 instead)
 *     description: Retrieves a list of artifacts for a specific vessel within a given time range. Supports filtering by time range, exclusion of specific IDs, and the option to include artifacts with null super categories.
 *     tags: [Artifacts]
 *     deprecated: true
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vesselName
 *         required: true
 *         schema:
 *           type: string
 *         description: The name or ID of the vessel to fetch artifacts for.
 *         example: "prototype-33"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startTimestamp:
 *                 required: false
 *                 type: integer
 *                 description: The start unix timestamp in milliseconds for filtering artifacts.
 *                 example: 1726876800000
 *               endTimestamp:
 *                 required: false
 *                 type: integer
 *                 description: The end unix timestamp in milliseconds for filtering artifacts.
 *                 example: 1726963200000
 *               excludeIds:
 *                 required: false
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: A list of artifact IDs to exclude from the results.
 *                 example: ["66ef4f6ea6db303e08c0767c", "66ef4f95a6db303e08c0767d"]
 *     responses:
 *       200:
 *         description: A list of artifacts for the specified vessel.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Artifact'
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this unit
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this unit.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */
