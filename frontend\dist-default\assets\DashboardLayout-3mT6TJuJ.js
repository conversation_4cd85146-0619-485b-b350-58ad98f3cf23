import{g as e,R as t,aM as n,ak as r,r as o,a as i,ay as s,j as a,V as l,G as c,K as p,x as u,T as d,Y as f,aN as h,ao as m,aO as g,ac as y,aP as b,ai as v,aQ as w,aR as x,aS as O,aT as S,a0 as E,L as j,aU as k,aV as C,aW as T,aX as P,aY as N,aZ as I,a_ as F,a$ as R,U as L,b0 as M,b1 as A}from"./vendor-DvOQ6qlC.js";import{e as D,u as z,t as W,d as _,a as B,b as H,v as G,m as U,p as V,o as q,r as Y,w as $}from"./index-DOw-VlvP.js";import{u as K}from"./AppHook-Pvyw2N1K.js";import{g as X}from"./gps_socket-Bc_SYb7T.js";import{a as J}from"./ArtifactFlag.controller-DDneHoIA.js";import"./utils-guRmN1PB.js";import"./maps-R0vlfPHe.js";import"./charts-Bh3hGOgg.js";function Q(e){return t=>typeof t===e}var Z=Q("function"),ee=e=>"RegExp"===Object.prototype.toString.call(e).slice(8,-1),te=e=>!ne(e)&&!(e=>null===e)(e)&&(Z(e)||"object"==typeof e),ne=Q("undefined");function re(e,t){if(e===t)return!0;if(e&&te(e)&&t&&te(t)){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)&&Array.isArray(t))return function(e,t){const{length:n}=e;if(n!==t.length)return!1;for(let r=n;0!=r--;)if(!re(e[r],t[r]))return!1;return!0}(e,t);if(e instanceof Map&&t instanceof Map)return function(e,t){if(e.size!==t.size)return!1;for(const n of e.entries())if(!t.has(n[0]))return!1;for(const n of e.entries())if(!re(n[1],t.get(n[0])))return!1;return!0}(e,t);if(e instanceof Set&&t instanceof Set)return function(e,t){if(e.size!==t.size)return!1;for(const n of e.entries())if(!t.has(n[0]))return!1;return!0}(e,t);if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return function(e,t){if(e.byteLength!==t.byteLength)return!1;const n=new DataView(e.buffer),r=new DataView(t.buffer);let o=e.byteLength;for(;o--;)if(n.getUint8(o)!==r.getUint8(o))return!1;return!0}(e,t);if(ee(e)&&ee(t))return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let e=n.length;0!=e--;)if(!Object.prototype.hasOwnProperty.call(t,n[e]))return!1;for(let o=n.length;0!=o--;){const r=n[o];if(("_owner"!==r||!e.$$typeof)&&!re(e[r],t[r]))return!1}return!0}return!(!Number.isNaN(e)||!Number.isNaN(t))||e===t}var oe=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],ie=["bigint","boolean","null","number","string","symbol","undefined"];function se(e){const t=Object.prototype.toString.call(e).slice(8,-1);return/HTML\w+Element/.test(t)?"HTMLElement":(n=t,oe.includes(n)?t:void 0);var n}function ae(e){return t=>se(t)===e}function le(e){return t=>typeof t===e}var ce=["innerHTML","ownerDocument","style","attributes","nodeValue"];function pe(e){if(null===e)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}if(pe.array(e))return"Array";if(pe.plainFunction(e))return"Function";const t=se(e);return t||"Object"}pe.array=Array.isArray,pe.arrayOf=(e,t)=>!(!pe.array(e)&&!pe.function(t))&&e.every((e=>t(e))),pe.asyncGeneratorFunction=e=>"AsyncGeneratorFunction"===se(e),pe.asyncFunction=ae("AsyncFunction"),pe.bigint=le("bigint"),pe.boolean=e=>!0===e||!1===e,pe.date=ae("Date"),pe.defined=e=>!pe.undefined(e),pe.domElement=e=>pe.object(e)&&!pe.plainObject(e)&&1===e.nodeType&&pe.string(e.nodeName)&&ce.every((t=>t in e)),pe.empty=e=>pe.string(e)&&0===e.length||pe.array(e)&&0===e.length||pe.object(e)&&!pe.map(e)&&!pe.set(e)&&0===Object.keys(e).length||pe.set(e)&&0===e.size||pe.map(e)&&0===e.size,pe.error=ae("Error"),pe.function=le("function"),pe.generator=e=>pe.iterable(e)&&pe.function(e.next)&&pe.function(e.throw),pe.generatorFunction=ae("GeneratorFunction"),pe.instanceOf=(e,t)=>!(!e||!t)&&Object.getPrototypeOf(e)===t.prototype,pe.iterable=e=>!pe.nullOrUndefined(e)&&pe.function(e[Symbol.iterator]),pe.map=ae("Map"),pe.nan=e=>Number.isNaN(e),pe.null=e=>null===e,pe.nullOrUndefined=e=>pe.null(e)||pe.undefined(e),pe.number=e=>le("number")(e)&&!pe.nan(e),pe.numericString=e=>pe.string(e)&&e.length>0&&!Number.isNaN(Number(e)),pe.object=e=>!pe.nullOrUndefined(e)&&(pe.function(e)||"object"==typeof e),pe.oneOf=(e,t)=>!!pe.array(e)&&e.indexOf(t)>-1,pe.plainFunction=ae("Function"),pe.plainObject=e=>{if("Object"!==se(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t===Object.getPrototypeOf({})},pe.primitive=e=>{return pe.null(e)||(t=typeof e,ie.includes(t));var t},pe.promise=ae("Promise"),pe.propertyOf=(e,t,n)=>{if(!pe.object(e)||!t)return!1;const r=e[t];return pe.function(n)?n(r):pe.defined(r)},pe.regexp=ae("RegExp"),pe.set=ae("Set"),pe.string=le("string"),pe.symbol=le("symbol"),pe.undefined=le("undefined"),pe.weakMap=ae("WeakMap"),pe.weakSet=ae("WeakSet");var ue,de,fe=pe;function he(e,t,n){const{actual:r,key:o,previous:i,type:s}=n,a=Oe(e,o),l=Oe(t,o);let c=[a,l].every(fe.number)&&("increased"===s?a<l:a>l);return fe.undefined(r)||(c=c&&l===r),fe.undefined(i)||(c=c&&a===i),c}function me(e,t,n){const{key:r,type:o,value:i}=n,s=Oe(e,r),a=Oe(t,r),l="added"===o?s:a,c="added"===o?a:s;return fe.nullOrUndefined(i)?[s,a].every(fe.array)?!c.every(we(l)):[s,a].every(fe.plainObject)?function(e,t){return t.some((t=>!e.includes(t)))}(Object.keys(l),Object.keys(c)):![s,a].every((e=>fe.primitive(e)&&fe.defined(e)))&&("added"===o?!fe.defined(s)&&fe.defined(a):fe.defined(s)&&!fe.defined(a)):fe.defined(l)?!(!fe.array(l)&&!fe.plainObject(l))&&function(e,t,n){return!!xe(e,t)&&([e,t].every(fe.array)?!e.some(be(n))&&t.some(be(n)):[e,t].every(fe.plainObject)?!Object.entries(e).some(ye(n))&&Object.entries(t).some(ye(n)):t===n)}(l,c,i):re(c,i)}function ge(e,t,{key:n}={}){let r=Oe(e,n),o=Oe(t,n);if(!xe(r,o))throw new TypeError("Inputs have different types");if(!function(...e){return e.every((e=>fe.string(e)||fe.array(e)||fe.plainObject(e)))}(r,o))throw new TypeError("Inputs don't have length");return[r,o].every(fe.plainObject)&&(r=Object.keys(r),o=Object.keys(o)),[r,o]}function ye(e){return([t,n])=>fe.array(e)?re(e,n)||e.some((e=>re(e,n)||fe.array(n)&&we(n)(e))):fe.plainObject(e)&&e[t]?!!e[t]&&re(e[t],n):re(e,n)}function be(e){return t=>fe.array(e)?e.some((e=>re(e,t)||fe.array(t)&&we(t)(e))):re(e,t)}function ve(e,t){return fe.array(e)?e.some((e=>re(e,t))):re(e,t)}function we(e){return t=>e.some((e=>re(e,t)))}function xe(...e){return e.every(fe.array)||e.every(fe.number)||e.every(fe.plainObject)||e.every(fe.string)}function Oe(e,t){if(fe.plainObject(e)||fe.array(e)){if(fe.string(t)){return t.split(".").reduce(((e,t)=>e&&e[t]),e)}return fe.number(t)?e[t]:e}return e}function Se(e,t){if([e,t].some(fe.nullOrUndefined))throw new Error("Missing required parameters");if(![e,t].every((e=>fe.plainObject(e)||fe.array(e))))throw new Error("Expected plain objects or array");return{added:(n,r)=>{try{return me(e,t,{key:n,type:"added",value:r})}catch{return!1}},changed:(n,r,o)=>{try{const i=Oe(e,n),s=Oe(t,n),a=fe.defined(r),l=fe.defined(o);if(a||l){const e=l?ve(o,i):!ve(r,i),t=ve(r,s);return e&&t}return[i,s].every(fe.array)||[i,s].every(fe.plainObject)?!re(i,s):i!==s}catch{return!1}},changedFrom:(n,r,o)=>{if(!fe.defined(n))return!1;try{const i=Oe(e,n),s=Oe(t,n),a=fe.defined(o);return ve(r,i)&&(a?ve(o,s):!a)}catch{return!1}},decreased:(n,r,o)=>{if(!fe.defined(n))return!1;try{return he(e,t,{key:n,actual:r,previous:o,type:"decreased"})}catch{return!1}},emptied:n=>{try{const[r,o]=ge(e,t,{key:n});return!!r.length&&!o.length}catch{return!1}},filled:n=>{try{const[r,o]=ge(e,t,{key:n});return!r.length&&!!o.length}catch{return!1}},increased:(n,r,o)=>{if(!fe.defined(n))return!1;try{return he(e,t,{key:n,actual:r,previous:o,type:"increased"})}catch{return!1}},removed:(n,r)=>{try{return me(e,t,{key:n,type:"removed",value:r})}catch{return!1}}}}var Ee=function(){if(de)return ue;de=1;var e=new Error("Element already at target scroll position"),t=new Error("Scroll cancelled"),n=Math.min,r=Date.now;function o(o){return function(a,l,c,p){"function"==typeof(c=c||{})&&(p=c,c={}),"function"!=typeof p&&(p=s);var u=r(),d=a[o],f=c.ease||i,h=isNaN(c.duration)?350:+c.duration,m=!1;return d===l?p(e,a[o]):requestAnimationFrame((function e(i){if(m)return p(t,a[o]);var s=r(),c=n(1,(s-u)/h),g=f(c);a[o]=g*(l-d)+d,c<1?requestAnimationFrame(e):requestAnimationFrame((function(){p(null,a[o])}))})),function(){m=!0}}}function i(e){return.5*(1-Math.cos(Math.PI*e))}function s(){}return ue={left:o("scrollLeft"),top:o("scrollTop")}}();const je=e(Ee);var ke,Ce,Te,Pe,Ne={exports:{}};const Ie=e((ke||(ke=1,Te=Ne.exports,Pe=function(){function e(e){var t=getComputedStyle(e,null).getPropertyValue("overflow");return t.indexOf("scroll")>-1||t.indexOf("auto")>-1}return function(t){if(t instanceof HTMLElement||t instanceof SVGElement){for(var n=t.parentNode;n.parentNode;){if(e(n))return n;n=n.parentNode}return document.scrollingElement||document.documentElement}}},(Ce=Ne).exports?Ce.exports=Pe():Te.Scrollparent=Pe()),Ne.exports));var Fe,Re;var Le=function(){if(Re)return Fe;Re=1;var e=function(e,n){return e+t(n)},t=function(n){return null===n||"boolean"==typeof n||void 0===n?"":"number"==typeof n?n.toString():"string"==typeof n?n:Array.isArray(n)?n.reduce(e,""):function(e){return Object.prototype.hasOwnProperty.call(e,"props")}(n)&&Object.prototype.hasOwnProperty.call(n.props,"children")?t(n.props.children):""};return t.default=t,Fe=t}();const Me=e(Le);var Ae,De;var ze=function(){if(De)return Ae;De=1;var e=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var n=Object.prototype.toString.call(e);return"[object RegExp]"===n||"[object Date]"===n||function(e){return e.$$typeof===t}(e)}(e)},t="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?a((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function r(e,t,r){return e.concat(t).map((function(e){return n(e,r)}))}function o(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function i(e,t){try{return t in e}catch(n){return!1}}function s(e,t,r){var s={};return r.isMergeableObject(e)&&o(e).forEach((function(t){s[t]=n(e[t],r)})),o(t).forEach((function(o){(function(e,t){return i(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,o)||(i(e,o)&&r.isMergeableObject(t[o])?s[o]=function(e,t){if(!t.customMerge)return a;var n=t.customMerge(e);return"function"==typeof n?n:a}(o,r)(e[o],t[o],r):s[o]=n(t[o],r))})),s}function a(t,o,i){(i=i||{}).arrayMerge=i.arrayMerge||r,i.isMergeableObject=i.isMergeableObject||e,i.cloneUnlessOtherwiseSpecified=n;var a=Array.isArray(o);return a===Array.isArray(t)?a?i.arrayMerge(t,o,i):s(t,o,i):n(o,i)}return a.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,n){return a(e,n,t)}),{})},Ae=a}();const We=e(ze);var _e="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,Be=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(_e&&navigator.userAgent.indexOf(e[t])>=0)return 1;return 0}();
/**!
 * @fileOverview Kickass library to create and place poppers near their reference elements.
 * @version 1.16.1
 * @license
 * Copyright (c) 2016 Federico Zivolo and contributors
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */var He=_e&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then((function(){t=!1,e()})))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout((function(){t=!1,e()}),Be))}};function Ge(e){return e&&"[object Function]"==={}.toString.call(e)}function Ue(e,t){if(1!==e.nodeType)return[];var n=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?n[t]:n}function Ve(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function qe(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=Ue(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/(auto|scroll|overlay)/.test(n+o+r)?e:qe(Ve(e))}function Ye(e){return e&&e.referenceNode?e.referenceNode:e}var $e=_e&&!(!window.MSInputMethodContext||!document.documentMode),Ke=_e&&/MSIE 10/.test(navigator.userAgent);function Xe(e){return 11===e?$e:10===e?Ke:$e||Ke}function Je(e){if(!e)return document.documentElement;for(var t=Xe(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var r=n&&n.nodeName;return r&&"BODY"!==r&&"HTML"!==r?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===Ue(n,"position")?Je(n):n:e?e.ownerDocument.documentElement:document.documentElement}function Qe(e){return null!==e.parentNode?Qe(e.parentNode):e}function Ze(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,r=n?e:t,o=n?t:e,i=document.createRange();i.setStart(r,0),i.setEnd(o,0);var s,a,l=i.commonAncestorContainer;if(e!==l&&t!==l||r.contains(o))return"BODY"===(a=(s=l).nodeName)||"HTML"!==a&&Je(s.firstElementChild)!==s?Je(l):l;var c=Qe(e);return c.host?Ze(c.host,t):Ze(e,Qe(t).host)}function et(e){var t="top"===(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top")?"scrollTop":"scrollLeft",n=e.nodeName;if("BODY"===n||"HTML"===n){var r=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||r)[t]}return e[t]}function tt(e,t){var n="x"===t?"Left":"Top",r="Left"===n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+r+"Width"])}function nt(e,t,n,r){return Math.max(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],Xe(10)?parseInt(n["offset"+e])+parseInt(r["margin"+("Height"===e?"Top":"Left")])+parseInt(r["margin"+("Height"===e?"Bottom":"Right")]):0)}function rt(e){var t=e.body,n=e.documentElement,r=Xe(10)&&getComputedStyle(n);return{height:nt("Height",t,n,r),width:nt("Width",t,n,r)}}var ot=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),it=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},st=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};function at(e){return st({},e,{right:e.left+e.width,bottom:e.top+e.height})}function lt(e){var t={};try{if(Xe(10)){t=e.getBoundingClientRect();var n=et(e,"top"),r=et(e,"left");t.top+=n,t.left+=r,t.bottom+=n,t.right+=r}else t=e.getBoundingClientRect()}catch(u){}var o={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},i="HTML"===e.nodeName?rt(e.ownerDocument):{},s=i.width||e.clientWidth||o.width,a=i.height||e.clientHeight||o.height,l=e.offsetWidth-s,c=e.offsetHeight-a;if(l||c){var p=Ue(e);l-=tt(p,"x"),c-=tt(p,"y"),o.width-=l,o.height-=c}return at(o)}function ct(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=Xe(10),o="HTML"===t.nodeName,i=lt(e),s=lt(t),a=qe(e),l=Ue(t),c=parseFloat(l.borderTopWidth),p=parseFloat(l.borderLeftWidth);n&&o&&(s.top=Math.max(s.top,0),s.left=Math.max(s.left,0));var u=at({top:i.top-s.top-c,left:i.left-s.left-p,width:i.width,height:i.height});if(u.marginTop=0,u.marginLeft=0,!r&&o){var d=parseFloat(l.marginTop),f=parseFloat(l.marginLeft);u.top-=c-d,u.bottom-=c-d,u.left-=p-f,u.right-=p-f,u.marginTop=d,u.marginLeft=f}return(r&&!n?t.contains(a):t===a&&"BODY"!==a.nodeName)&&(u=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=et(t,"top"),o=et(t,"left"),i=n?-1:1;return e.top+=r*i,e.bottom+=r*i,e.left+=o*i,e.right+=o*i,e}(u,t)),u}function pt(e){var t=e.nodeName;if("BODY"===t||"HTML"===t)return!1;if("fixed"===Ue(e,"position"))return!0;var n=Ve(e);return!!n&&pt(n)}function ut(e){if(!e||!e.parentElement||Xe())return document.documentElement;for(var t=e.parentElement;t&&"none"===Ue(t,"transform");)t=t.parentElement;return t||document.documentElement}function dt(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i={top:0,left:0},s=o?ut(e):Ze(e,Ye(t));if("viewport"===r)i=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.ownerDocument.documentElement,r=ct(e,n),o=Math.max(n.clientWidth,window.innerWidth||0),i=Math.max(n.clientHeight,window.innerHeight||0),s=t?0:et(n),a=t?0:et(n,"left");return at({top:s-r.top+r.marginTop,left:a-r.left+r.marginLeft,width:o,height:i})}(s,o);else{var a=void 0;"scrollParent"===r?"BODY"===(a=qe(Ve(t))).nodeName&&(a=e.ownerDocument.documentElement):a="window"===r?e.ownerDocument.documentElement:r;var l=ct(a,s,o);if("HTML"!==a.nodeName||pt(s))i=l;else{var c=rt(e.ownerDocument),p=c.height,u=c.width;i.top+=l.top-l.marginTop,i.bottom=p+l.top,i.left+=l.left-l.marginLeft,i.right=u+l.left}}var d="number"==typeof(n=n||0);return i.left+=d?n:n.left||0,i.top+=d?n:n.top||0,i.right-=d?n:n.right||0,i.bottom-=d?n:n.bottom||0,i}function ft(e,t,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===e.indexOf("auto"))return e;var s=dt(n,r,i,o),a={top:{width:s.width,height:t.top-s.top},right:{width:s.right-t.right,height:s.height},bottom:{width:s.width,height:s.bottom-t.bottom},left:{width:t.left-s.left,height:s.height}},l=Object.keys(a).map((function(e){return st({key:e},a[e],{area:(t=a[e],t.width*t.height)});var t})).sort((function(e,t){return t.area-e.area})),c=l.filter((function(e){var t=e.width,r=e.height;return t>=n.clientWidth&&r>=n.clientHeight})),p=c.length>0?c[0].key:l[0].key,u=e.split("-")[1];return p+(u?"-"+u:"")}function ht(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return ct(n,r?ut(t):Ze(t,Ye(n)),r)}function mt(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),r=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+r,height:e.offsetHeight+n}}function gt(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,(function(e){return t[e]}))}function yt(e,t,n){n=n.split("-")[0];var r=mt(e),o={width:r.width,height:r.height},i=-1!==["right","left"].indexOf(n),s=i?"top":"left",a=i?"left":"top",l=i?"height":"width",c=i?"width":"height";return o[s]=t[s]+t[l]/2-r[l]/2,o[a]=n===a?t[a]-r[c]:t[gt(a)],o}function bt(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function vt(e,t,n){return(void 0===n?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex((function(e){return e[t]===n}));var r=bt(e,(function(e){return e[t]===n}));return e.indexOf(r)}(e,"name",n))).forEach((function(e){e.function;var n=e.function||e.fn;e.enabled&&Ge(n)&&(t.offsets.popper=at(t.offsets.popper),t.offsets.reference=at(t.offsets.reference),t=n(t,e))})),t}function wt(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=ht(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=ft(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=yt(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=vt(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function xt(e,t){return e.some((function(e){var n=e.name;return e.enabled&&n===t}))}function Ot(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),r=0;r<t.length;r++){var o=t[r],i=o?""+o+n:e;if(void 0!==document.body.style[i])return i}return null}function St(){return this.state.isDestroyed=!0,xt(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[Ot("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function Et(e){var t=e.ownerDocument;return t?t.defaultView:window}function jt(e,t,n,r){var o="BODY"===e.nodeName,i=o?e.ownerDocument.defaultView:e;i.addEventListener(t,n,{passive:!0}),o||jt(qe(i.parentNode),t,n,r),r.push(i)}function kt(e,t,n,r){n.updateBound=r,Et(e).addEventListener("resize",n.updateBound,{passive:!0});var o=qe(e);return jt(o,"scroll",n.updateBound,n.scrollParents),n.scrollElement=o,n.eventsEnabled=!0,n}function Ct(){this.state.eventsEnabled||(this.state=kt(this.reference,this.options,this.state,this.scheduleUpdate))}function Tt(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,Et(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach((function(e){e.removeEventListener("scroll",t.updateBound)})),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function Pt(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function Nt(e,t){Object.keys(t).forEach((function(n){var r="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&Pt(t[n])&&(r="px"),e.style[n]=t[n]+r}))}var It=_e&&/Firefox/i.test(navigator.userAgent);function Ft(e,t,n){var r=bt(e,(function(e){return e.name===t})),o=!!r&&e.some((function(e){return e.name===n&&e.enabled&&e.order<r.order}));if(!o);return o}var Rt=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Lt=Rt.slice(3);function Mt(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=Lt.indexOf(e),r=Lt.slice(n+1).concat(Lt.slice(0,n));return t?r.reverse():r}var At="flip",Dt="clockwise",zt="counterclockwise";function Wt(e,t,n,r){var o=[0,0],i=-1!==["right","left"].indexOf(r),s=e.split(/(\+|\-)/).map((function(e){return e.trim()})),a=s.indexOf(bt(s,(function(e){return-1!==e.search(/,|\s/)})));s[a]&&s[a].indexOf(",");var l=/\s*,\s*|\s+/,c=-1!==a?[s.slice(0,a).concat([s[a].split(l)[0]]),[s[a].split(l)[1]].concat(s.slice(a+1))]:[s];return c=c.map((function(e,r){var o=(1===r?!i:i)?"height":"width",s=!1;return e.reduce((function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,s=!0,e):s?(e[e.length-1]+=t,s=!1,e):e.concat(t)}),[]).map((function(e){return function(e,t,n,r){var o=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),i=+o[1],s=o[2];if(!i)return e;if(0===s.indexOf("%")){return at("%p"===s?n:r)[t]/100*i}if("vh"===s||"vw"===s)return("vh"===s?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*i;return i}(e,o,t,n)}))})),c.forEach((function(e,t){e.forEach((function(n,r){Pt(n)&&(o[t]+=n*("-"===e[r-1]?-1:1))}))})),o}var _t,Bt,Ht={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,n=t.split("-")[0],r=t.split("-")[1];if(r){var o=e.offsets,i=o.reference,s=o.popper,a=-1!==["bottom","top"].indexOf(n),l=a?"left":"top",c=a?"width":"height",p={start:it({},l,i[l]),end:it({},l,i[l]+i[c]-s[c])};e.offsets.popper=st({},s,p[r])}return e}},offset:{order:200,enabled:!0,fn:function(e,t){var n=t.offset,r=e.placement,o=e.offsets,i=o.popper,s=o.reference,a=r.split("-")[0],l=void 0;return l=Pt(+n)?[+n,0]:Wt(n,i,s,a),"left"===a?(i.top+=l[0],i.left-=l[1]):"right"===a?(i.top+=l[0],i.left+=l[1]):"top"===a?(i.left+=l[0],i.top-=l[1]):"bottom"===a&&(i.left+=l[0],i.top+=l[1]),e.popper=i,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,t){var n=t.boundariesElement||Je(e.instance.popper);e.instance.reference===n&&(n=Je(n));var r=Ot("transform"),o=e.instance.popper.style,i=o.top,s=o.left,a=o[r];o.top="",o.left="",o[r]="";var l=dt(e.instance.popper,e.instance.reference,t.padding,n,e.positionFixed);o.top=i,o.left=s,o[r]=a,t.boundaries=l;var c=t.priority,p=e.offsets.popper,u={primary:function(e){var n=p[e];return p[e]<l[e]&&!t.escapeWithReference&&(n=Math.max(p[e],l[e])),it({},e,n)},secondary:function(e){var n="right"===e?"left":"top",r=p[n];return p[e]>l[e]&&!t.escapeWithReference&&(r=Math.min(p[n],l[e]-("right"===e?p.width:p.height))),it({},n,r)}};return c.forEach((function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";p=st({},p,u[t](e))})),e.offsets.popper=p,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,r=t.reference,o=e.placement.split("-")[0],i=Math.floor,s=-1!==["top","bottom"].indexOf(o),a=s?"right":"bottom",l=s?"left":"top",c=s?"width":"height";return n[a]<i(r[l])&&(e.offsets.popper[l]=i(r[l])-n[c]),n[l]>i(r[a])&&(e.offsets.popper[l]=i(r[a])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){var n;if(!Ft(e.instance.modifiers,"arrow","keepTogether"))return e;var r=t.element;if("string"==typeof r){if(!(r=e.instance.popper.querySelector(r)))return e}else if(!e.instance.popper.contains(r))return e;var o=e.placement.split("-")[0],i=e.offsets,s=i.popper,a=i.reference,l=-1!==["left","right"].indexOf(o),c=l?"height":"width",p=l?"Top":"Left",u=p.toLowerCase(),d=l?"left":"top",f=l?"bottom":"right",h=mt(r)[c];a[f]-h<s[u]&&(e.offsets.popper[u]-=s[u]-(a[f]-h)),a[u]+h>s[f]&&(e.offsets.popper[u]+=a[u]+h-s[f]),e.offsets.popper=at(e.offsets.popper);var m=a[u]+a[c]/2-h/2,g=Ue(e.instance.popper),y=parseFloat(g["margin"+p]),b=parseFloat(g["border"+p+"Width"]),v=m-e.offsets.popper[u]-y-b;return v=Math.max(Math.min(s[c]-h,v),0),e.arrowElement=r,e.offsets.arrow=(it(n={},u,Math.round(v)),it(n,d,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(e,t){if(xt(e.instance.modifiers,"inner"))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var n=dt(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),r=e.placement.split("-")[0],o=gt(r),i=e.placement.split("-")[1]||"",s=[];switch(t.behavior){case At:s=[r,o];break;case Dt:s=Mt(r);break;case zt:s=Mt(r,!0);break;default:s=t.behavior}return s.forEach((function(a,l){if(r!==a||s.length===l+1)return e;r=e.placement.split("-")[0],o=gt(r);var c=e.offsets.popper,p=e.offsets.reference,u=Math.floor,d="left"===r&&u(c.right)>u(p.left)||"right"===r&&u(c.left)<u(p.right)||"top"===r&&u(c.bottom)>u(p.top)||"bottom"===r&&u(c.top)<u(p.bottom),f=u(c.left)<u(n.left),h=u(c.right)>u(n.right),m=u(c.top)<u(n.top),g=u(c.bottom)>u(n.bottom),y="left"===r&&f||"right"===r&&h||"top"===r&&m||"bottom"===r&&g,b=-1!==["top","bottom"].indexOf(r),v=!!t.flipVariations&&(b&&"start"===i&&f||b&&"end"===i&&h||!b&&"start"===i&&m||!b&&"end"===i&&g),w=!!t.flipVariationsByContent&&(b&&"start"===i&&h||b&&"end"===i&&f||!b&&"start"===i&&g||!b&&"end"===i&&m),x=v||w;(d||y||x)&&(e.flipped=!0,(d||y)&&(r=s[l+1]),x&&(i=function(e){return"end"===e?"start":"start"===e?"end":e}(i)),e.placement=r+(i?"-"+i:""),e.offsets.popper=st({},e.offsets.popper,yt(e.instance.popper,e.offsets.reference,e.placement)),e=vt(e.instance.modifiers,e,"flip"))})),e},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],r=e.offsets,o=r.popper,i=r.reference,s=-1!==["left","right"].indexOf(n),a=-1===["top","left"].indexOf(n);return o[s?"left":"top"]=i[n]-(a?o[s?"width":"height"]:0),e.placement=gt(t),e.offsets.popper=at(o),e}},hide:{order:800,enabled:!0,fn:function(e){if(!Ft(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=bt(e.instance.modifiers,(function(e){return"preventOverflow"===e.name})).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n=t.x,r=t.y,o=e.offsets.popper,i=bt(e.instance.modifiers,(function(e){return"applyStyle"===e.name})).gpuAcceleration,s=void 0!==i?i:t.gpuAcceleration,a=Je(e.instance.popper),l=lt(a),c={position:o.position},p=function(e,t){var n=e.offsets,r=n.popper,o=n.reference,i=Math.round,s=Math.floor,a=function(e){return e},l=i(o.width),c=i(r.width),p=-1!==["left","right"].indexOf(e.placement),u=-1!==e.placement.indexOf("-"),d=t?p||u||l%2==c%2?i:s:a,f=t?i:a;return{left:d(l%2==1&&c%2==1&&!u&&t?r.left-1:r.left),top:f(r.top),bottom:f(r.bottom),right:d(r.right)}}(e,window.devicePixelRatio<2||!It),u="bottom"===n?"top":"bottom",d="right"===r?"left":"right",f=Ot("transform"),h=void 0,m=void 0;if(m="bottom"===u?"HTML"===a.nodeName?-a.clientHeight+p.bottom:-l.height+p.bottom:p.top,h="right"===d?"HTML"===a.nodeName?-a.clientWidth+p.right:-l.width+p.right:p.left,s&&f)c[f]="translate3d("+h+"px, "+m+"px, 0)",c[u]=0,c[d]=0,c.willChange="transform";else{var g="bottom"===u?-1:1,y="right"===d?-1:1;c[u]=m*g,c[d]=h*y,c.willChange=u+", "+d}var b={"x-placement":e.placement};return e.attributes=st({},b,e.attributes),e.styles=st({},c,e.styles),e.arrowStyles=st({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,n;return Nt(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach((function(e){!1!==n[e]?t.setAttribute(e,n[e]):t.removeAttribute(e)})),e.arrowElement&&Object.keys(e.arrowStyles).length&&Nt(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,n,r,o){var i=ht(o,t,e,n.positionFixed),s=ft(n.placement,i,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",s),Nt(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},Gt=function(){function e(t,n){var r=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(r.update)},this.update=He(this.update.bind(this)),this.options=st({},e.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=n&&n.jquery?n[0]:n,this.options.modifiers={},Object.keys(st({},e.Defaults.modifiers,o.modifiers)).forEach((function(t){r.options.modifiers[t]=st({},e.Defaults.modifiers[t]||{},o.modifiers?o.modifiers[t]:{})})),this.modifiers=Object.keys(this.options.modifiers).map((function(e){return st({name:e},r.options.modifiers[e])})).sort((function(e,t){return e.order-t.order})),this.modifiers.forEach((function(e){e.enabled&&Ge(e.onLoad)&&e.onLoad(r.reference,r.popper,r.options,e,r.state)})),this.update();var i=this.options.eventsEnabled;i&&this.enableEventListeners(),this.state.eventsEnabled=i}return ot(e,[{key:"update",value:function(){return wt.call(this)}},{key:"destroy",value:function(){return St.call(this)}},{key:"enableEventListeners",value:function(){return Ct.call(this)}},{key:"disableEventListeners",value:function(){return Tt.call(this)}}]),e}();Gt.Utils=("undefined"!=typeof window?window:{}).PopperUtils,Gt.placements=Rt,Gt.Defaults=Ht;var Ut=function(){if(Bt)return _t;Bt=1;var e=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var n=Object.prototype.toString.call(e);return"[object RegExp]"===n||"[object Date]"===n||function(e){return e.$$typeof===t}(e)}(e)},t="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?a((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function r(e,t,r){return e.concat(t).map((function(e){return n(e,r)}))}function o(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function i(e,t){try{return t in e}catch(n){return!1}}function s(e,t,r){var s={};return r.isMergeableObject(e)&&o(e).forEach((function(t){s[t]=n(e[t],r)})),o(t).forEach((function(o){(function(e,t){return i(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,o)||(i(e,o)&&r.isMergeableObject(t[o])?s[o]=function(e,t){if(!t.customMerge)return a;var n=t.customMerge(e);return"function"==typeof n?n:a}(o,r)(e[o],t[o],r):s[o]=n(t[o],r))})),s}function a(t,o,i){(i=i||{}).arrayMerge=i.arrayMerge||r,i.isMergeableObject=i.isMergeableObject||e,i.cloneUnlessOtherwiseSpecified=n;var a=Array.isArray(o);return a===Array.isArray(t)?a?i.arrayMerge(t,o,i):s(t,o,i):n(o,i)}return a.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,n){return a(e,n,t)}),{})},_t=a}();const Vt=e(Ut);var qt=["innerHTML","ownerDocument","style","attributes","nodeValue"],Yt=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],$t=["bigint","boolean","null","number","string","symbol","undefined"];function Kt(e){var t,n=Object.prototype.toString.call(e).slice(8,-1);return/HTML\w+Element/.test(n)?"HTMLElement":(t=n,Yt.includes(t)?n:void 0)}function Xt(e){return function(t){return Kt(t)===e}}function Jt(e){return function(t){return typeof t===e}}function Qt(e){if(null===e)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}if(Qt.array(e))return"Array";if(Qt.plainFunction(e))return"Function";var t=Kt(e);return t||"Object"}function Zt(e){return function(t){return typeof t===e}}Qt.array=Array.isArray,Qt.arrayOf=function(e,t){return!(!Qt.array(e)&&!Qt.function(t))&&e.every((function(e){return t(e)}))},Qt.asyncGeneratorFunction=function(e){return"AsyncGeneratorFunction"===Kt(e)},Qt.asyncFunction=Xt("AsyncFunction"),Qt.bigint=Jt("bigint"),Qt.boolean=function(e){return!0===e||!1===e},Qt.date=Xt("Date"),Qt.defined=function(e){return!Qt.undefined(e)},Qt.domElement=function(e){return Qt.object(e)&&!Qt.plainObject(e)&&1===e.nodeType&&Qt.string(e.nodeName)&&qt.every((function(t){return t in e}))},Qt.empty=function(e){return Qt.string(e)&&0===e.length||Qt.array(e)&&0===e.length||Qt.object(e)&&!Qt.map(e)&&!Qt.set(e)&&0===Object.keys(e).length||Qt.set(e)&&0===e.size||Qt.map(e)&&0===e.size},Qt.error=Xt("Error"),Qt.function=Jt("function"),Qt.generator=function(e){return Qt.iterable(e)&&Qt.function(e.next)&&Qt.function(e.throw)},Qt.generatorFunction=Xt("GeneratorFunction"),Qt.instanceOf=function(e,t){return!(!e||!t)&&Object.getPrototypeOf(e)===t.prototype},Qt.iterable=function(e){return!Qt.nullOrUndefined(e)&&Qt.function(e[Symbol.iterator])},Qt.map=Xt("Map"),Qt.nan=function(e){return Number.isNaN(e)},Qt.null=function(e){return null===e},Qt.nullOrUndefined=function(e){return Qt.null(e)||Qt.undefined(e)},Qt.number=function(e){return Jt("number")(e)&&!Qt.nan(e)},Qt.numericString=function(e){return Qt.string(e)&&e.length>0&&!Number.isNaN(Number(e))},Qt.object=function(e){return!Qt.nullOrUndefined(e)&&(Qt.function(e)||"object"==typeof e)},Qt.oneOf=function(e,t){return!!Qt.array(e)&&e.indexOf(t)>-1},Qt.plainFunction=Xt("Function"),Qt.plainObject=function(e){if("Object"!==Kt(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.getPrototypeOf({})},Qt.primitive=function(e){return Qt.null(e)||(t=typeof e,$t.includes(t));var t},Qt.promise=Xt("Promise"),Qt.propertyOf=function(e,t,n){if(!Qt.object(e)||!t)return!1;var r=e[t];return Qt.function(n)?n(r):Qt.defined(r)},Qt.regexp=Xt("RegExp"),Qt.set=Xt("Set"),Qt.string=Jt("string"),Qt.symbol=Jt("symbol"),Qt.undefined=Jt("undefined"),Qt.weakMap=Xt("WeakMap"),Qt.weakSet=Xt("WeakSet");var en=Zt("function"),tn=function(e){return"RegExp"===Object.prototype.toString.call(e).slice(8,-1)},nn=function(e){return!rn(e)&&!function(e){return null===e}(e)&&(en(e)||"object"==typeof e)},rn=Zt("undefined"),on=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};function sn(e,t){if(e===t)return!0;if(e&&nn(e)&&t&&nn(t)){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)&&Array.isArray(t))return function(e,t){var n=e.length;if(n!==t.length)return!1;for(var r=n;0!=r--;)if(!sn(e[r],t[r]))return!1;return!0}(e,t);if(e instanceof Map&&t instanceof Map)return function(e,t){var n,r,o,i;if(e.size!==t.size)return!1;try{for(var s=on(e.entries()),a=s.next();!a.done;a=s.next()){var l=a.value;if(!t.has(l[0]))return!1}}catch(u){n={error:u}}finally{try{a&&!a.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}try{for(var c=on(e.entries()),p=c.next();!p.done;p=c.next())if(!sn((l=p.value)[1],t.get(l[0])))return!1}catch(d){o={error:d}}finally{try{p&&!p.done&&(i=c.return)&&i.call(c)}finally{if(o)throw o.error}}return!0}(e,t);if(e instanceof Set&&t instanceof Set)return function(e,t){var n,r;if(e.size!==t.size)return!1;try{for(var o=on(e.entries()),i=o.next();!i.done;i=o.next()){var s=i.value;if(!t.has(s[0]))return!1}}catch(a){n={error:a}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return!0}(e,t);if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return function(e,t){if(e.byteLength!==t.byteLength)return!1;for(var n=new DataView(e.buffer),r=new DataView(t.buffer),o=e.byteLength;o--;)if(n.getUint8(o)!==r.getUint8(o))return!1;return!0}(e,t);if(tn(e)&&tn(t))return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var o=n.length;0!=o--;)if(!Object.prototype.hasOwnProperty.call(t,n[o]))return!1;for(o=n.length;0!=o--;){var i=n[o];if(("_owner"!==i||!e.$$typeof)&&!sn(e[i],t[i]))return!1}return!0}return!(!Number.isNaN(e)||!Number.isNaN(t))||e===t}function an(e,t,n){var r=n.actual,o=n.key,i=n.previous,s=n.type,a=mn(e,o),l=mn(t,o),c=[a,l].every(Qt.number)&&("increased"===s?a<l:a>l);return Qt.undefined(r)||(c=c&&l===r),Qt.undefined(i)||(c=c&&a===i),c}function ln(e,t,n){var r=n.key,o=n.type,i=n.value,s=mn(e,r),a=mn(t,r),l="added"===o?s:a,c="added"===o?a:s;return Qt.nullOrUndefined(i)?[s,a].every(Qt.array)?!c.every(fn(l)):[s,a].every(Qt.plainObject)?function(e,t){return t.some((function(t){return!e.includes(t)}))}(Object.keys(l),Object.keys(c)):![s,a].every((function(e){return Qt.primitive(e)&&Qt.defined(e)}))&&("added"===o?!Qt.defined(s)&&Qt.defined(a):Qt.defined(s)&&!Qt.defined(a)):Qt.defined(l)?!(!Qt.array(l)&&!Qt.plainObject(l))&&function(e,t,n){return!!hn(e,t)&&([e,t].every(Qt.array)?!e.some(un(n))&&t.some(un(n)):[e,t].every(Qt.plainObject)?!Object.entries(e).some(pn(n))&&Object.entries(t).some(pn(n)):t===n)}(l,c,i):sn(c,i)}function cn(e,t,n){var r=(void 0===n?{}:n).key,o=mn(e,r),i=mn(t,r);if(!hn(o,i))throw new TypeError("Inputs have different types");if(!function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.every((function(e){return Qt.string(e)||Qt.array(e)||Qt.plainObject(e)}))}(o,i))throw new TypeError("Inputs don't have length");return[o,i].every(Qt.plainObject)&&(o=Object.keys(o),i=Object.keys(i)),[o,i]}function pn(e){return function(t){var n=t[0],r=t[1];return Qt.array(e)?sn(e,r)||e.some((function(e){return sn(e,r)||Qt.array(r)&&fn(r)(e)})):Qt.plainObject(e)&&e[n]?!!e[n]&&sn(e[n],r):sn(e,r)}}function un(e){return function(t){return Qt.array(e)?e.some((function(e){return sn(e,t)||Qt.array(t)&&fn(t)(e)})):sn(e,t)}}function dn(e,t){return Qt.array(e)?e.some((function(e){return sn(e,t)})):sn(e,t)}function fn(e){return function(t){return e.some((function(e){return sn(e,t)}))}}function hn(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.every(Qt.array)||e.every(Qt.number)||e.every(Qt.plainObject)||e.every(Qt.string)}function mn(e,t){return Qt.plainObject(e)||Qt.array(e)?Qt.string(t)?t.split(".").reduce((function(e,t){return e&&e[t]}),e):Qt.number(t)?e[t]:e:e}function gn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function yn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?gn(Object(n),!0).forEach((function(t){wn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function bn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function vn(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Cn(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function wn(e,t,n){return(t=Cn(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function xn(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Sn(e,t)}function On(e){return On=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},On(e)}function Sn(e,t){return Sn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Sn(e,t)}function En(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function jn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function kn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=On(e);if(t){var o=On(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return jn(e)}(this,n)}}function Cn(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}var Tn={flip:{padding:20},preventOverflow:{padding:10}};function Pn(e,t,n){return function(e,t){if("function"!=typeof e)throw new TypeError("The typeValidator argument must be a function with the signature function(props, propName, componentName).");if(Boolean(t))throw new TypeError("The error message is optional, but must be a string if provided.")}(e,n),function(n,r,o){for(var i=arguments.length,s=new Array(i>3?i-3:0),a=3;a<i;a++)s[a-3]=arguments[a];return function(e,t,n,r){return"boolean"==typeof e?e:"function"==typeof e?e(t,n,r):!0===Boolean(e)&&Boolean(e)}(t,n,r,o)?function(e,t){return Object.hasOwnProperty.call(e,t)}(n,r)?e.apply(void 0,[n,r,o].concat(s)):function(e,t,n){return new Error("Required ".concat(e[t]," `").concat(t,"` was not specified in `").concat(n,"`."))}(n,r,o):e.apply(void 0,[n,r,o].concat(s))}}var Nn={INIT:"init",IDLE:"idle",OPENING:"opening",OPEN:"open",CLOSING:"closing"},In=void 0!==n.createPortal;function Fn(){return!("undefined"==typeof window||!window.document||!window.document.createElement)}function Rn(){return"ontouchstart"in window&&/Mobi/.test(navigator.userAgent)}function Ln(e){var t=e.title,n=e.data,r=e.warn,o=void 0!==r&&r,i=e.debug,s=void 0!==i&&i,a=o?console.warn||console.error:console.log;s&&t&&n&&(Array.isArray(n)?n.forEach((function(e){Qt.plainObject(e)&&e.key?a.apply(console,[e.key,e.value]):a.apply(console,[e])})):a.apply(console,[n]))}function Mn(){}var An=function(){xn(r,t.Component);var e=kn(r);function r(){return bn(this,r),e.apply(this,arguments)}return vn(r,[{key:"componentDidMount",value:function(){Fn()&&(this.node||this.appendNode(),In||this.renderPortal())}},{key:"componentDidUpdate",value:function(){Fn()&&(In||this.renderPortal())}},{key:"componentWillUnmount",value:function(){Fn()&&this.node&&(In||n.unmountComponentAtNode(this.node),this.node&&this.node.parentNode===document.body&&(document.body.removeChild(this.node),this.node=void 0))}},{key:"appendNode",value:function(){var e=this.props,t=e.id,n=e.zIndex;this.node||(this.node=document.createElement("div"),t&&(this.node.id=t),n&&(this.node.style.zIndex=n),document.body.appendChild(this.node))}},{key:"renderPortal",value:function(){if(!Fn())return null;var e=this.props,r=e.children,o=e.setRef;return this.node||this.appendNode(),In?n.createPortal(r,this.node):(o(n.unstable_renderSubtreeIntoContainer(this,r.length>1?t.createElement("div",null,r):r[0],this.node)),null)}},{key:"renderReact16",value:function(){var e=this.props,t=e.hasChildren,n=e.placement,r=e.target;return t||r||"center"===n?this.renderPortal():null}},{key:"render",value:function(){return In?this.renderReact16():null}}]),r}();wn(An,"propTypes",{children:r.oneOfType([r.element,r.array]),hasChildren:r.bool,id:r.oneOfType([r.string,r.number]),placement:r.string,setRef:r.func.isRequired,target:r.oneOfType([r.object,r.string]),zIndex:r.number});var Dn=function(){xn(n,t.Component);var e=kn(n);function n(){return bn(this,n),e.apply(this,arguments)}return vn(n,[{key:"parentStyle",get:function(){var e=this.props,t=e.placement,n=e.styles.arrow.length,r={pointerEvents:"none",position:"absolute",width:"100%"};return t.startsWith("top")?(r.bottom=0,r.left=0,r.right=0,r.height=n):t.startsWith("bottom")?(r.left=0,r.right=0,r.top=0,r.height=n):t.startsWith("left")?(r.right=0,r.top=0,r.bottom=0):t.startsWith("right")&&(r.left=0,r.top=0),r}},{key:"render",value:function(){var e,n=this.props,r=n.placement,o=n.setArrowRef,i=n.styles.arrow,s=i.color,a=i.display,l=i.length,c=i.margin,p=i.position,u=i.spread,d={display:a,position:p},f=u,h=l;return r.startsWith("top")?(e="0,0 ".concat(f/2,",").concat(h," ").concat(f,",0"),d.bottom=0,d.marginLeft=c,d.marginRight=c):r.startsWith("bottom")?(e="".concat(f,",").concat(h," ").concat(f/2,",0 0,").concat(h),d.top=0,d.marginLeft=c,d.marginRight=c):r.startsWith("left")?(h=u,e="0,0 ".concat(f=l,",").concat(h/2," 0,").concat(h),d.right=0,d.marginTop=c,d.marginBottom=c):r.startsWith("right")&&(h=u,e="".concat(f=l,",").concat(h," ").concat(f,",0 0,").concat(h/2),d.left=0,d.marginTop=c,d.marginBottom=c),t.createElement("div",{className:"__floater__arrow",style:this.parentStyle},t.createElement("span",{ref:o,style:d},t.createElement("svg",{width:f,height:h,version:"1.1",xmlns:"http://www.w3.org/2000/svg"},t.createElement("polygon",{points:e,fill:s}))))}}]),n}();wn(Dn,"propTypes",{placement:r.string.isRequired,setArrowRef:r.func.isRequired,styles:r.object.isRequired});var zn=["color","height","width"];function Wn(e){var n=e.handleClick,r=e.styles,o=r.color,i=r.height,s=r.width,a=En(r,zn);return t.createElement("button",{"aria-label":"close",onClick:n,style:a,type:"button"},t.createElement("svg",{width:"".concat(s,"px"),height:"".concat(i,"px"),viewBox:"0 0 18 18",version:"1.1",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid"},t.createElement("g",null,t.createElement("path",{d:"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z",fill:o}))))}function _n(e){var n=e.content,r=e.footer,o=e.handleClick,i=e.open,s=e.positionWrapper,a=e.showCloseButton,l=e.title,c=e.styles,p={content:t.isValidElement(n)?n:t.createElement("div",{className:"__floater__content",style:c.content},n)};return l&&(p.title=t.isValidElement(l)?l:t.createElement("div",{className:"__floater__title",style:c.title},l)),r&&(p.footer=t.isValidElement(r)?r:t.createElement("div",{className:"__floater__footer",style:c.footer},r)),!a&&!s||Qt.boolean(i)||(p.close=t.createElement(Wn,{styles:c.close,handleClick:o})),t.createElement("div",{className:"__floater__container",style:c.container},p.close,p.title,p.content,p.footer)}Wn.propTypes={handleClick:r.func.isRequired,styles:r.object.isRequired},_n.propTypes={content:r.node.isRequired,footer:r.node,handleClick:r.func.isRequired,open:r.bool,positionWrapper:r.bool.isRequired,showCloseButton:r.bool.isRequired,styles:r.object.isRequired,title:r.node};var Bn=function(){xn(n,t.Component);var e=kn(n);function n(){return bn(this,n),e.apply(this,arguments)}return vn(n,[{key:"style",get:function(){var e=this.props,t=e.disableAnimation,n=e.component,r=e.placement,o=e.hideArrow,i=e.status,s=e.styles,a=s.arrow.length,l=s.floater,c=s.floaterCentered,p=s.floaterClosing,u=s.floaterOpening,d=s.floaterWithAnimation,f=s.floaterWithComponent,h={};return o||(r.startsWith("top")?h.padding="0 0 ".concat(a,"px"):r.startsWith("bottom")?h.padding="".concat(a,"px 0 0"):r.startsWith("left")?h.padding="0 ".concat(a,"px 0 0"):r.startsWith("right")&&(h.padding="0 0 0 ".concat(a,"px"))),-1!==[Nn.OPENING,Nn.OPEN].indexOf(i)&&(h=yn(yn({},h),u)),i===Nn.CLOSING&&(h=yn(yn({},h),p)),i!==Nn.OPEN||t||(h=yn(yn({},h),d)),"center"===r&&(h=yn(yn({},h),c)),n&&(h=yn(yn({},h),f)),yn(yn({},l),h)}},{key:"render",value:function(){var e=this.props,n=e.component,r=e.handleClick,o=e.hideArrow,i=e.setFloaterRef,s=e.status,a={},l=["__floater"];return a.content=n?t.isValidElement(n)?t.cloneElement(n,{closeFn:r}):n({closeFn:r}):t.createElement(_n,this.props),s===Nn.OPEN&&l.push("__floater__open"),o||(a.arrow=t.createElement(Dn,this.props)),t.createElement("div",{ref:i,className:l.join(" "),style:this.style},t.createElement("div",{className:"__floater__body"},a.content,a.arrow))}}]),n}();wn(Bn,"propTypes",{component:r.oneOfType([r.func,r.element]),content:r.node,disableAnimation:r.bool.isRequired,footer:r.node,handleClick:r.func.isRequired,hideArrow:r.bool.isRequired,open:r.bool,placement:r.string.isRequired,positionWrapper:r.bool.isRequired,setArrowRef:r.func.isRequired,setFloaterRef:r.func.isRequired,showCloseButton:r.bool,status:r.string.isRequired,styles:r.object.isRequired,title:r.node});var Hn=function(){xn(n,t.Component);var e=kn(n);function n(){return bn(this,n),e.apply(this,arguments)}return vn(n,[{key:"render",value:function(){var e,n=this.props,r=n.children,o=n.handleClick,i=n.handleMouseEnter,s=n.handleMouseLeave,a=n.setChildRef,l=n.setWrapperRef,c=n.style,p=n.styles;if(r)if(1===t.Children.count(r))if(t.isValidElement(r)){var u=Qt.function(r.type)?"innerRef":"ref";e=t.cloneElement(t.Children.only(r),wn({},u,a))}else e=t.createElement("span",null,r);else e=r;return e?t.createElement("span",{ref:l,style:yn(yn({},p),c),onClick:o,onMouseEnter:i,onMouseLeave:s},e):null}}]),n}();wn(Hn,"propTypes",{children:r.node,handleClick:r.func.isRequired,handleMouseEnter:r.func.isRequired,handleMouseLeave:r.func.isRequired,setChildRef:r.func.isRequired,setWrapperRef:r.func.isRequired,style:r.object,styles:r.object.isRequired});var Gn={zIndex:100};var Un=["arrow","flip","offset"],Vn=["position","top","right","bottom","left"],qn=function(){xn(n,t.Component);var e=kn(n);function n(t){var r;return bn(this,n),wn(jn(r=e.call(this,t)),"setArrowRef",(function(e){r.arrowRef=e})),wn(jn(r),"setChildRef",(function(e){r.childRef=e})),wn(jn(r),"setFloaterRef",(function(e){r.floaterRef=e})),wn(jn(r),"setWrapperRef",(function(e){r.wrapperRef=e})),wn(jn(r),"handleTransitionEnd",(function(){var e=r.state.status,t=r.props.callback;r.wrapperPopper&&r.wrapperPopper.instance.update(),r.setState({status:e===Nn.OPENING?Nn.OPEN:Nn.IDLE},(function(){var e=r.state.status;t(e===Nn.OPEN?"open":"close",r.props)}))})),wn(jn(r),"handleClick",(function(){var e=r.props,t=e.event,n=e.open;if(!Qt.boolean(n)){var o=r.state,i=o.positionWrapper,s=o.status;("click"===r.event||"hover"===r.event&&i)&&(Ln({title:"click",data:[{event:t,status:s===Nn.OPEN?"closing":"opening"}],debug:r.debug}),r.toggle())}})),wn(jn(r),"handleMouseEnter",(function(){var e=r.props,t=e.event,n=e.open;if(!Qt.boolean(n)&&!Rn()){var o=r.state.status;"hover"===r.event&&o===Nn.IDLE&&(Ln({title:"mouseEnter",data:[{key:"originalEvent",value:t}],debug:r.debug}),clearTimeout(r.eventDelayTimeout),r.toggle())}})),wn(jn(r),"handleMouseLeave",(function(){var e=r.props,t=e.event,n=e.eventDelay,o=e.open;if(!Qt.boolean(o)&&!Rn()){var i=r.state,s=i.status,a=i.positionWrapper;"hover"===r.event&&(Ln({title:"mouseLeave",data:[{key:"originalEvent",value:t}],debug:r.debug}),n?-1===[Nn.OPENING,Nn.OPEN].indexOf(s)||a||r.eventDelayTimeout||(r.eventDelayTimeout=setTimeout((function(){delete r.eventDelayTimeout,r.toggle()}),1e3*n)):r.toggle(Nn.IDLE))}})),r.state={currentPlacement:t.placement,needsUpdate:!1,positionWrapper:t.wrapperOptions.position&&!!t.target,status:Nn.INIT,statusWrapper:Nn.INIT},r._isMounted=!1,r.hasMounted=!1,Fn()&&window.addEventListener("load",(function(){r.popper&&r.popper.instance.update(),r.wrapperPopper&&r.wrapperPopper.instance.update()})),r}return vn(n,[{key:"componentDidMount",value:function(){if(Fn()){var e=this.state.positionWrapper,t=this.props,n=t.children,r=t.open,o=t.target;this._isMounted=!0,Ln({title:"init",data:{hasChildren:!!n,hasTarget:!!o,isControlled:Qt.boolean(r),positionWrapper:e,target:this.target,floater:this.floaterRef},debug:this.debug}),this.hasMounted||(this.initPopper(),this.hasMounted=!0),!n&&o&&Qt.boolean(r)}}},{key:"componentDidUpdate",value:function(e,t){if(Fn()){var n,r=this.props,o=r.autoOpen,i=r.open,s=r.target,a=r.wrapperOptions,l=function(e,t){if([e,t].some(Qt.nullOrUndefined))throw new Error("Missing required parameters");if(![e,t].every((function(e){return Qt.plainObject(e)||Qt.array(e)})))throw new Error("Expected plain objects or array");var n=function(n,r,o){try{var i=mn(e,n),s=mn(t,n),a=Qt.defined(r),l=Qt.defined(o);if(a||l){var c=l?dn(o,i):!dn(r,i),p=dn(r,s);return c&&p}return[i,s].every(Qt.array)||[i,s].every(Qt.plainObject)?!sn(i,s):i!==s}catch(u){return!1}};return{added:function(n,r){try{return ln(e,t,{key:n,type:"added",value:r})}catch(o){return!1}},changed:n,changedFrom:function(n,r,o){if(!Qt.defined(n))return!1;try{var i=mn(e,n),s=mn(t,n),a=Qt.defined(o);return dn(r,i)&&(a?dn(o,s):!a)}catch(l){return!1}},changedTo:function(e,t){return!!Qt.defined(e)&&n(e,t)},decreased:function(n,r,o){if(!Qt.defined(n))return!1;try{return an(e,t,{key:n,actual:r,previous:o,type:"decreased"})}catch(i){return!1}},emptied:function(n){try{var r=cn(e,t,{key:n}),o=r[0],i=r[1];return!!o.length&&!i.length}catch(s){return!1}},filled:function(n){try{var r=cn(e,t,{key:n}),o=r[0],i=r[1];return!o.length&&!!i.length}catch(s){return!1}},increased:function(n,r,o){if(!Qt.defined(n))return!1;try{return an(e,t,{key:n,actual:r,previous:o,type:"increased"})}catch(i){return!1}},removed:function(n,r){try{return ln(e,t,{key:n,type:"removed",value:r})}catch(o){return!1}}}}(t,this.state),c=l.changedFrom,p=l.changed;if(e.open!==i)Qt.boolean(i)&&(n=i?Nn.OPENING:Nn.CLOSING),this.toggle(n);e.wrapperOptions.position===a.position&&e.target===s||this.changeWrapperPosition(this.props),(p("status",Nn.IDLE)&&i||c("status",Nn.INIT,Nn.IDLE)&&o)&&this.toggle(Nn.OPEN),this.popper&&p("status",Nn.OPENING)&&this.popper.instance.update(),this.floaterRef&&(p("status",Nn.OPENING)||p("status",Nn.CLOSING))&&function(e,t,n){var r;r=function(o){n(o),function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];e.removeEventListener(t,n,r)}(e,t,r)},function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];e.addEventListener(t,n,r)}(e,t,r,arguments.length>3&&void 0!==arguments[3]&&arguments[3])}(this.floaterRef,"transitionend",this.handleTransitionEnd),p("needsUpdate",!0)&&this.rebuildPopper()}}},{key:"componentWillUnmount",value:function(){Fn()&&(this._isMounted=!1,this.popper&&this.popper.instance.destroy(),this.wrapperPopper&&this.wrapperPopper.instance.destroy())}},{key:"initPopper",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.target,n=this.state.positionWrapper,r=this.props,o=r.disableFlip,i=r.getPopper,s=r.hideArrow,a=r.offset,l=r.placement,c=r.wrapperOptions,p="top"===l||"bottom"===l?"flip":["right","bottom-end","top-end","left","top-start","bottom-start"];if("center"===l)this.setState({status:Nn.IDLE});else if(t&&this.floaterRef){var u=this.options,d=u.arrow,f=u.flip,h=u.offset,m=En(u,Un);new Gt(t,this.floaterRef,{placement:l,modifiers:yn({arrow:yn({enabled:!s,element:this.arrowRef},d),flip:yn({enabled:!o,behavior:p},f),offset:yn({offset:"0, ".concat(a,"px")},h)},m),onCreate:function(t){var n;e.popper=t,null!==(n=e.floaterRef)&&void 0!==n&&n.isConnected?(i(t,"floater"),e._isMounted&&e.setState({currentPlacement:t.placement,status:Nn.IDLE}),l!==t.placement&&setTimeout((function(){t.instance.update()}),1)):e.setState({needsUpdate:!0})},onUpdate:function(t){e.popper=t;var n=e.state.currentPlacement;e._isMounted&&t.placement!==n&&e.setState({currentPlacement:t.placement})}})}if(n){var g=Qt.undefined(c.offset)?0:c.offset;new Gt(this.target,this.wrapperRef,{placement:c.placement||l,modifiers:{arrow:{enabled:!1},offset:{offset:"0, ".concat(g,"px")},flip:{enabled:!1}},onCreate:function(t){e.wrapperPopper=t,e._isMounted&&e.setState({statusWrapper:Nn.IDLE}),i(t,"wrapper"),l!==t.placement&&setTimeout((function(){t.instance.update()}),1)}})}}},{key:"rebuildPopper",value:function(){var e=this;this.floaterRefInterval=setInterval((function(){var t;null!==(t=e.floaterRef)&&void 0!==t&&t.isConnected&&(clearInterval(e.floaterRefInterval),e.setState({needsUpdate:!1}),e.initPopper())}),50)}},{key:"changeWrapperPosition",value:function(e){var t=e.target,n=e.wrapperOptions;this.setState({positionWrapper:n.position&&!!t})}},{key:"toggle",value:function(e){var t=this.state.status===Nn.OPEN?Nn.CLOSING:Nn.OPENING;Qt.undefined(e)||(t=e),this.setState({status:t})}},{key:"debug",get:function(){return this.props.debug||Fn()&&"ReactFloaterDebug"in window&&!!window.ReactFloaterDebug}},{key:"event",get:function(){var e=this.props,t=e.disableHoverToClick,n=e.event;return"hover"===n&&Rn()&&!t?"click":n}},{key:"options",get:function(){var e=this.props.options;return Vt(Tn,e||{})}},{key:"styles",get:function(){var e,t=this,n=this.state,r=n.status,o=n.positionWrapper,i=n.statusWrapper,s=this.props.styles,a=Vt(function(e){var t=Vt(Gn,e.options||{});return{wrapper:{cursor:"help",display:"inline-flex",flexDirection:"column",zIndex:t.zIndex},wrapperPosition:{left:-1e3,position:"absolute",top:-1e3,visibility:"hidden"},floater:{display:"inline-block",filter:"drop-shadow(0 0 3px rgba(0, 0, 0, 0.3))",maxWidth:300,opacity:0,position:"relative",transition:"opacity 0.3s",visibility:"hidden",zIndex:t.zIndex},floaterOpening:{opacity:1,visibility:"visible"},floaterWithAnimation:{opacity:1,transition:"opacity 0.3s, transform 0.2s",visibility:"visible"},floaterWithComponent:{maxWidth:"100%"},floaterClosing:{opacity:0,visibility:"visible"},floaterCentered:{left:"50%",position:"fixed",top:"50%",transform:"translate(-50%, -50%)"},container:{backgroundColor:"#fff",color:"#666",minHeight:60,minWidth:200,padding:20,position:"relative",zIndex:10},title:{borderBottom:"1px solid #555",color:"#555",fontSize:18,marginBottom:5,paddingBottom:6,paddingRight:18},content:{fontSize:15},close:{backgroundColor:"transparent",border:0,borderRadius:0,color:"#555",fontSize:0,height:15,outline:"none",padding:10,position:"absolute",right:0,top:0,width:15,WebkitAppearance:"none"},footer:{borderTop:"1px solid #ccc",fontSize:13,marginTop:10,paddingTop:5},arrow:{color:"#fff",display:"inline-flex",length:16,margin:8,position:"absolute",spread:32},options:t}}(s),s);o&&(e=-1===[Nn.IDLE].indexOf(r)||-1===[Nn.IDLE].indexOf(i)?a.wrapperPosition:this.wrapperPopper.styles,a.wrapper=yn(yn({},a.wrapper),e));if(this.target){var l=window.getComputedStyle(this.target);this.wrapperStyles?a.wrapper=yn(yn({},a.wrapper),this.wrapperStyles):-1===["relative","static"].indexOf(l.position)&&(this.wrapperStyles={},o||(Vn.forEach((function(e){t.wrapperStyles[e]=l[e]})),a.wrapper=yn(yn({},a.wrapper),this.wrapperStyles),this.target.style.position="relative",this.target.style.top="auto",this.target.style.right="auto",this.target.style.bottom="auto",this.target.style.left="auto"))}return a}},{key:"target",get:function(){if(!Fn())return null;var e=this.props.target;return e?Qt.domElement(e)?e:document.querySelector(e):this.childRef||this.wrapperRef}},{key:"render",value:function(){var e=this.state,n=e.currentPlacement,r=e.positionWrapper,o=e.status,i=this.props,s=i.children,a=i.component,l=i.content,c=i.disableAnimation,p=i.footer,u=i.hideArrow,d=i.id,f=i.open,h=i.showCloseButton,m=i.style,g=i.target,y=i.title,b=t.createElement(Hn,{handleClick:this.handleClick,handleMouseEnter:this.handleMouseEnter,handleMouseLeave:this.handleMouseLeave,setChildRef:this.setChildRef,setWrapperRef:this.setWrapperRef,style:m,styles:this.styles.wrapper},s),v={};return r?v.wrapperInPortal=b:v.wrapperAsChildren=b,t.createElement("span",null,t.createElement(An,{hasChildren:!!s,id:d,placement:n,setRef:this.setFloaterRef,target:g,zIndex:this.styles.options.zIndex},t.createElement(Bn,{component:a,content:l,disableAnimation:c,footer:p,handleClick:this.handleClick,hideArrow:u||"center"===n,open:f,placement:n,positionWrapper:r,setArrowRef:this.setArrowRef,setFloaterRef:this.setFloaterRef,showCloseButton:h,status:o,styles:this.styles,title:y}),v.wrapperInPortal),v.wrapperAsChildren)}}]),n}();wn(qn,"propTypes",{autoOpen:r.bool,callback:r.func,children:r.node,component:Pn(r.oneOfType([r.func,r.element]),(function(e){return!e.content})),content:Pn(r.node,(function(e){return!e.component})),debug:r.bool,disableAnimation:r.bool,disableFlip:r.bool,disableHoverToClick:r.bool,event:r.oneOf(["hover","click"]),eventDelay:r.number,footer:r.node,getPopper:r.func,hideArrow:r.bool,id:r.oneOfType([r.string,r.number]),offset:r.number,open:r.bool,options:r.object,placement:r.oneOf(["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end","auto","center"]),showCloseButton:r.bool,style:r.object,styles:r.object,target:r.oneOfType([r.object,r.string]),title:r.node,wrapperOptions:r.shape({offset:r.number,placement:r.oneOf(["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end","auto"]),position:r.bool})}),wn(qn,"defaultProps",{autoOpen:!1,callback:Mn,debug:!1,disableAnimation:!1,disableFlip:!1,disableHoverToClick:!1,event:"click",eventDelay:.4,getPopper:Mn,hideArrow:!1,offset:15,placement:"bottom",showCloseButton:!1,styles:{},target:null,wrapperOptions:{position:!1}});var Yn=Object.defineProperty,$n=(e,t,n)=>((e,t,n)=>t in e?Yn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n)(e,"symbol"!=typeof t?t+"":t,n),Kn="init",Xn="start",Jn="stop",Qn="reset",Zn="prev",er="next",tr="go",nr="close",rr="skip",or="update",ir="tour:start",sr="step:before",ar="beacon",lr="tooltip",cr="step:after",pr="tour:end",ur="tour:status",dr="error:target_not_found",fr={INIT:"init",READY:"ready",BEACON:"beacon",TOOLTIP:"tooltip",COMPLETE:"complete",ERROR:"error"},hr={IDLE:"idle",READY:"ready",WAITING:"waiting",RUNNING:"running",PAUSED:"paused",SKIPPED:"skipped",FINISHED:"finished"};function mr(){var e;return!("undefined"==typeof window||!(null==(e=window.document)?void 0:e.createElement))}function gr(e){return e?e.getBoundingClientRect():null}function yr(e=!1){const{body:t,documentElement:n}=document;if(!t||!n)return 0;if(e){const e=[t.scrollHeight,t.offsetHeight,n.clientHeight,n.scrollHeight,n.offsetHeight].sort(((e,t)=>e-t)),r=Math.floor(e.length/2);return e.length%2==0?(e[r-1]+e[r])/2:e[r]}return Math.max(t.scrollHeight,t.offsetHeight,n.clientHeight,n.scrollHeight,n.offsetHeight)}function br(e){if("string"==typeof e)try{return document.querySelector(e)}catch(t){return null}return e}function vr(e,t,n){if(!e)return Sr();const r=Ie(e);if(r){if(r.isSameNode(Sr()))return n?document:Sr();if(!(r.scrollHeight>r.offsetHeight)&&!t)return r.style.overflow="initial",Sr()}return r}function wr(e,t){if(!e)return!1;const n=vr(e,t);return!!n&&!n.isSameNode(Sr())}function xr(e,t="fixed"){if(!(e&&e instanceof HTMLElement))return!1;const{nodeName:n}=e,r=function(e){return e&&1===e.nodeType?getComputedStyle(e):null}(e);return"BODY"!==n&&"HTML"!==n&&(!(!r||r.position!==t)||!!e.parentNode&&xr(e.parentNode,t))}function Or(e,t,n){var r;if(!e)return 0;const{offsetTop:o=0,scrollTop:i=0}=null!=(r=Ie(e))?r:{};let s=e.getBoundingClientRect().top+i;o&&(wr(e,n)||function(e){return e.offsetParent!==document.body}(e))&&(s-=o);const a=Math.floor(s-t);return a<0?0:a}function Sr(){var e;return null!=(e=document.scrollingElement)?e:document.documentElement}var Er=void 0!==i.createPortal;function jr(e=navigator.userAgent){let t=e;return"undefined"==typeof window?t="node":document.documentMode?t="ie":/Edge/.test(e)?t="edge":Boolean(window.opera)||e.includes(" OPR/")?t="opera":void 0!==window.InstallTrigger?t="firefox":window.chrome?t="chrome":/(Version\/([\d._]+).*Safari|CriOS|FxiOS| Mobile\/)/.test(e)&&(t="safari"),t}function kr(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function Cr(e,t={}){const{defaultValue:n,step:r,steps:i}=t;let s=Me(e);if(s)(s.includes("{step}")||s.includes("{steps}"))&&r&&i&&(s=s.replace("{step}",r.toString()).replace("{steps}",i.toString()));else if(o.isValidElement(e)&&!Object.values(e.props).length&&"function"===kr(e.type)){s=Cr(e.type({}),t)}else s=Me(n);return s}function Tr(e){const t=e.replace(/^#?([\da-f])([\da-f])([\da-f])$/i,((e,t,n,r)=>t+t+n+n+r+r)),n=/^#?([\da-f]{2})([\da-f]{2})([\da-f]{2})$/i.exec(t);return n?[parseInt(n[1],16),parseInt(n[2],16),parseInt(n[3],16)]:[]}function Pr(e){return e.disableBeacon||"center"===e.placement}function Nr(){return!["chrome","safari","firefox","opera"].includes(jr())}function Ir({data:e,debug:t=!1,title:n,warn:r=!1}){const o=r?console.warn||console.error:console.log;t&&n&&e&&(Array.isArray(e)?e.forEach((e=>{fe.plainObject(e)&&e.key?o.apply(console,[e.key,e.value]):o.apply(console,[e])})):o.apply(console,[e]))}function Fr(e,...t){if(!fe.plainObject(e))throw new TypeError("Expected an object");const n={};for(const r in e)({}).hasOwnProperty.call(e,r)&&(t.includes(r)||(n[r]=e[r]));return n}function Rr(e,t,n){const r=e=>e.replace("{step}",String(t)).replace("{steps}",String(n));if("string"===kr(e))return r(e);if(!o.isValidElement(e))return e;const{children:i}=e.props;if("string"===kr(i)&&i.includes("{step}"))return o.cloneElement(e,{children:r(i)});if(Array.isArray(i))return o.cloneElement(e,{children:i.map((e=>"string"==typeof e?r(e):Rr(e,t,n)))});if("function"===kr(e.type)&&!Object.values(e.props).length){return Rr(e.type({}),t,n)}return e}var Lr={options:{preventOverflow:{boundariesElement:"scrollParent"}},wrapperOptions:{offset:-18,position:!0}},Mr={back:"Back",close:"Close",last:"Last",next:"Next",nextLabelWithProgress:"Next (Step {step} of {steps})",open:"Open the dialog",skip:"Skip"},Ar={event:"click",placement:"bottom",offset:10,disableBeacon:!1,disableCloseOnEsc:!1,disableOverlay:!1,disableOverlayClose:!1,disableScrollParentFix:!1,disableScrolling:!1,hideBackButton:!1,hideCloseButton:!1,hideFooter:!1,isFixed:!1,locale:Mr,showProgress:!1,showSkipButton:!1,spotlightClicks:!1,spotlightPadding:10},Dr={continuous:!1,debug:!1,disableCloseOnEsc:!1,disableOverlay:!1,disableOverlayClose:!1,disableScrolling:!1,disableScrollParentFix:!1,getHelpers:void 0,hideBackButton:!1,run:!0,scrollOffset:20,scrollDuration:300,scrollToFirstStep:!1,showSkipButton:!1,showProgress:!1,spotlightClicks:!1,spotlightPadding:10,steps:[]},zr={arrowColor:"#fff",backgroundColor:"#fff",beaconSize:36,overlayColor:"rgba(0, 0, 0, 0.5)",primaryColor:"#f04",spotlightShadow:"0 0 15px rgba(0, 0, 0, 0.5)",textColor:"#333",width:380,zIndex:100},Wr={backgroundColor:"transparent",border:0,borderRadius:0,color:"#555",cursor:"pointer",fontSize:16,lineHeight:1,padding:8,WebkitAppearance:"none"},_r={borderRadius:4,position:"absolute"};function Br(e){return function(e,...t){if(!fe.plainObject(e))throw new TypeError("Expected an object");if(!t.length)return e;const n={};for(const r in e)({}).hasOwnProperty.call(e,r)&&t.includes(r)&&(n[r]=e[r]);return n}(e,"beaconComponent","disableCloseOnEsc","disableOverlay","disableOverlayClose","disableScrolling","disableScrollParentFix","floaterProps","hideBackButton","hideCloseButton","locale","showProgress","showSkipButton","spotlightClicks","spotlightPadding","styles","tooltipComponent")}function Hr(e,t){var n,r,o,i,s,a;const l=null!=t?t:{},c=We.all([Ar,Br(e),l],{isMergeableObject:fe.plainObject}),p=function(e,t){var n,r,o,i,s;const{floaterProps:a,styles:l}=e,c=We(null!=(n=t.floaterProps)?n:{},null!=a?a:{}),p=We(null!=l?l:{},null!=(r=t.styles)?r:{}),u=We(zr,p.options||{}),d="center"===t.placement||t.disableBeacon;let{width:f}=u;window.innerWidth>480&&(f=380),"width"in u&&(f="number"==typeof u.width&&window.innerWidth<u.width?window.innerWidth-30:u.width);const h={bottom:0,left:0,overflow:"hidden",position:"absolute",right:0,top:0,zIndex:u.zIndex},m={beacon:{...Wr,display:d?"none":"inline-block",height:u.beaconSize,position:"relative",width:u.beaconSize,zIndex:u.zIndex},beaconInner:{animation:"joyride-beacon-inner 1.2s infinite ease-in-out",backgroundColor:u.primaryColor,borderRadius:"50%",display:"block",height:"50%",left:"50%",opacity:.7,position:"absolute",top:"50%",transform:"translate(-50%, -50%)",width:"50%"},beaconOuter:{animation:"joyride-beacon-outer 1.2s infinite ease-in-out",backgroundColor:`rgba(${Tr(u.primaryColor).join(",")}, 0.2)`,border:`2px solid ${u.primaryColor}`,borderRadius:"50%",boxSizing:"border-box",display:"block",height:"100%",left:0,opacity:.9,position:"absolute",top:0,transformOrigin:"center",width:"100%"},tooltip:{backgroundColor:u.backgroundColor,borderRadius:5,boxSizing:"border-box",color:u.textColor,fontSize:16,maxWidth:"100%",padding:15,position:"relative",width:f},tooltipContainer:{lineHeight:1.4,textAlign:"center"},tooltipTitle:{fontSize:18,margin:0},tooltipContent:{padding:"20px 10px"},tooltipFooter:{alignItems:"center",display:"flex",justifyContent:"flex-end",marginTop:15},tooltipFooterSpacer:{flex:1},buttonNext:{...Wr,backgroundColor:u.primaryColor,borderRadius:4,color:"#fff"},buttonBack:{...Wr,color:u.primaryColor,marginLeft:"auto",marginRight:5},buttonClose:{...Wr,color:u.textColor,height:14,padding:15,position:"absolute",right:0,top:0,width:14},buttonSkip:{...Wr,color:u.textColor,fontSize:14},overlay:{...h,backgroundColor:u.overlayColor,mixBlendMode:"hard-light"},overlayLegacy:{...h},overlayLegacyCenter:{...h,backgroundColor:u.overlayColor},spotlight:{..._r,backgroundColor:"gray"},spotlightLegacy:{..._r,boxShadow:`0 0 0 9999px ${u.overlayColor}, ${u.spotlightShadow}`},floaterStyles:{arrow:{color:null!=(s=null==(i=null==(o=null==c?void 0:c.styles)?void 0:o.arrow)?void 0:i.color)?s:u.arrowColor},options:{zIndex:u.zIndex+100}},options:u};return We(m,p)}(e,c),u=wr(br(c.target),c.disableScrollParentFix),d=We.all([Lr,null!=(n=e.floaterProps)?n:{},null!=(r=c.floaterProps)?r:{}]);return d.offset=c.offset,d.styles=We(null!=(o=d.styles)?o:{},p.floaterStyles),d.offset+=null!=(s=null!=(i=e.spotlightPadding)?i:c.spotlightPadding)?s:0,c.placementBeacon&&d.wrapperOptions&&(d.wrapperOptions.placement=c.placementBeacon),u&&d.options.preventOverflow&&(d.options.preventOverflow.boundariesElement="window"),{...c,locale:We.all([Mr,null!=(a=e.locale)?a:{},c.locale||{}]),floaterProps:d,styles:Fr(p,"floaterStyles")}}function Gr(e,t=!1){return fe.plainObject(e)?!!e.target||(Ir({title:"validateStep",data:"target is missing from the step",warn:!0,debug:t}),!1):(Ir({title:"validateStep",data:"step must be an object",warn:!0,debug:t}),!1)}function Ur(e,t=!1){return fe.array(e)?e.every((e=>Gr(e,t))):(Ir({title:"validateSteps",data:"steps must be an array",warn:!0,debug:t}),!1)}var Vr,qr={action:"init",controlled:!1,index:0,lifecycle:fr.INIT,origin:null,size:0,status:hr.IDLE},Yr=(Vr=Fr(qr,"controlled","size"),Object.keys(Vr)),$r=class{constructor(e){$n(this,"beaconPopper"),$n(this,"tooltipPopper"),$n(this,"data",new Map),$n(this,"listener"),$n(this,"store",new Map),$n(this,"addListener",(e=>{this.listener=e})),$n(this,"setSteps",(e=>{const{size:t,status:n}=this.getState(),r={size:e.length,status:n};this.data.set("steps",e),n===hr.WAITING&&!t&&e.length&&(r.status=hr.RUNNING),this.setState(r)})),$n(this,"getPopper",(e=>"beacon"===e?this.beaconPopper:this.tooltipPopper)),$n(this,"setPopper",((e,t)=>{"beacon"===e?this.beaconPopper=t:this.tooltipPopper=t})),$n(this,"cleanupPoppers",(()=>{this.beaconPopper=null,this.tooltipPopper=null})),$n(this,"close",((e=null)=>{const{index:t,status:n}=this.getState();n===hr.RUNNING&&this.setState({...this.getNextState({action:nr,index:t+1,origin:e})})})),$n(this,"go",(e=>{const{controlled:t,status:n}=this.getState();if(t||n!==hr.RUNNING)return;const r=this.getSteps()[e];this.setState({...this.getNextState({action:tr,index:e}),status:r?n:hr.FINISHED})})),$n(this,"info",(()=>this.getState())),$n(this,"next",(()=>{const{index:e,status:t}=this.getState();t===hr.RUNNING&&this.setState(this.getNextState({action:er,index:e+1}))})),$n(this,"open",(()=>{const{status:e}=this.getState();e===hr.RUNNING&&this.setState({...this.getNextState({action:or,lifecycle:fr.TOOLTIP})})})),$n(this,"prev",(()=>{const{index:e,status:t}=this.getState();t===hr.RUNNING&&this.setState({...this.getNextState({action:Zn,index:e-1})})})),$n(this,"reset",((e=!1)=>{const{controlled:t}=this.getState();t||this.setState({...this.getNextState({action:Qn,index:0}),status:e?hr.RUNNING:hr.READY})})),$n(this,"skip",(()=>{const{status:e}=this.getState();e===hr.RUNNING&&this.setState({action:rr,lifecycle:fr.INIT,status:hr.SKIPPED})})),$n(this,"start",(e=>{const{index:t,size:n}=this.getState();this.setState({...this.getNextState({action:Xn,index:fe.number(e)?e:t},!0),status:n?hr.RUNNING:hr.WAITING})})),$n(this,"stop",((e=!1)=>{const{index:t,status:n}=this.getState();[hr.FINISHED,hr.SKIPPED].includes(n)||this.setState({...this.getNextState({action:Jn,index:t+(e?1:0)}),status:hr.PAUSED})})),$n(this,"update",(e=>{var t,n,r,o;if(r=e,o=Yr,!(fe.plainObject(r)&&fe.array(o)&&Object.keys(r).every((e=>o.includes(e)))))throw new Error(`State is not valid. Valid keys: ${Yr.join(", ")}`);this.setState({...this.getNextState({...this.getState(),...e,action:null!=(t=e.action)?t:or,origin:null!=(n=e.origin)?n:null},!0)})}));const{continuous:t=!1,stepIndex:n,steps:r=[]}=null!=e?e:{};this.setState({action:Kn,controlled:fe.number(n),continuous:t,index:fe.number(n)?n:0,lifecycle:fr.INIT,origin:null,status:r.length?hr.READY:hr.IDLE},!0),this.beaconPopper=null,this.tooltipPopper=null,this.listener=null,this.setSteps(r)}getState(){return this.store.size?{action:this.store.get("action")||"",controlled:this.store.get("controlled")||!1,index:parseInt(this.store.get("index"),10),lifecycle:this.store.get("lifecycle")||"",origin:this.store.get("origin")||null,size:this.store.get("size")||0,status:this.store.get("status")||""}:{...qr}}getNextState(e,t=!1){var n,r,o,i,s;const{action:a,controlled:l,index:c,size:p,status:u}=this.getState(),d=fe.number(e.index)?e.index:c,f=l&&!t?c:Math.min(Math.max(d,0),p);return{action:null!=(n=e.action)?n:a,controlled:l,index:f,lifecycle:null!=(r=e.lifecycle)?r:fr.INIT,origin:null!=(o=e.origin)?o:null,size:null!=(i=e.size)?i:p,status:f===p?hr.FINISHED:null!=(s=e.status)?s:u}}getSteps(){const e=this.data.get("steps");return Array.isArray(e)?e:[]}hasUpdatedState(e){return JSON.stringify(e)!==JSON.stringify(this.getState())}setState(e,t=!1){const n=this.getState(),{action:r,index:o,lifecycle:i,origin:s=null,size:a,status:l}={...n,...e};this.store.set("action",r),this.store.set("index",o),this.store.set("lifecycle",i),this.store.set("origin",s),this.store.set("size",a),this.store.set("status",l),t&&(this.store.set("controlled",e.controlled),this.store.set("continuous",e.continuous)),this.listener&&this.hasUpdatedState(n)&&this.listener(this.getState())}getHelpers(){return{close:this.close,go:this.go,info:this.info,next:this.next,open:this.open,prev:this.prev,reset:this.reset,skip:this.skip}}};var Kr=function({styles:e}){return o.createElement("div",{key:"JoyrideSpotlight",className:"react-joyride__spotlight","data-test-id":"spotlight",style:e})},Xr=class extends o.Component{constructor(){super(...arguments),$n(this,"isActive",!1),$n(this,"resizeTimeout"),$n(this,"scrollTimeout"),$n(this,"scrollParent"),$n(this,"state",{isScrolling:!1,mouseOverSpotlight:!1,showSpotlight:!0}),$n(this,"hideSpotlight",(()=>{const{continuous:e,disableOverlay:t,lifecycle:n}=this.props,r=[fr.INIT,fr.BEACON,fr.COMPLETE,fr.ERROR];return t||(e?r.includes(n):n!==fr.TOOLTIP)})),$n(this,"handleMouseMove",(e=>{const{mouseOverSpotlight:t}=this.state,{height:n,left:r,position:o,top:i,width:s}=this.spotlightStyles,a="fixed"===o?e.clientY:e.pageY,l="fixed"===o?e.clientX:e.pageX,c=l>=r&&l<=r+s&&(a>=i&&a<=i+n);c!==t&&this.updateState({mouseOverSpotlight:c})})),$n(this,"handleScroll",(()=>{const{target:e}=this.props,t=br(e);if(this.scrollParent!==document){const{isScrolling:e}=this.state;e||this.updateState({isScrolling:!0,showSpotlight:!1}),clearTimeout(this.scrollTimeout),this.scrollTimeout=window.setTimeout((()=>{this.updateState({isScrolling:!1,showSpotlight:!0})}),50)}else xr(t,"sticky")&&this.updateState({})})),$n(this,"handleResize",(()=>{clearTimeout(this.resizeTimeout),this.resizeTimeout=window.setTimeout((()=>{this.isActive&&this.forceUpdate()}),100)}))}componentDidMount(){const{debug:e,disableScrolling:t,disableScrollParentFix:n=!1,target:r}=this.props,o=br(r);this.scrollParent=vr(null!=o?o:document.body,n,!0),this.isActive=!0,window.addEventListener("resize",this.handleResize)}componentDidUpdate(e){var t;const{disableScrollParentFix:n,lifecycle:r,spotlightClicks:o,target:i}=this.props,{changed:s}=Se(e,this.props);if(s("target")||s("disableScrollParentFix")){const e=br(i);this.scrollParent=vr(null!=e?e:document.body,n,!0)}s("lifecycle",fr.TOOLTIP)&&(null==(t=this.scrollParent)||t.addEventListener("scroll",this.handleScroll,{passive:!0}),setTimeout((()=>{const{isScrolling:e}=this.state;e||this.updateState({showSpotlight:!0})}),100)),(s("spotlightClicks")||s("disableOverlay")||s("lifecycle"))&&(o&&r===fr.TOOLTIP?window.addEventListener("mousemove",this.handleMouseMove,!1):r!==fr.TOOLTIP&&window.removeEventListener("mousemove",this.handleMouseMove))}componentWillUnmount(){var e;this.isActive=!1,window.removeEventListener("mousemove",this.handleMouseMove),window.removeEventListener("resize",this.handleResize),clearTimeout(this.resizeTimeout),clearTimeout(this.scrollTimeout),null==(e=this.scrollParent)||e.removeEventListener("scroll",this.handleScroll)}get overlayStyles(){const{mouseOverSpotlight:e}=this.state,{disableOverlayClose:t,placement:n,styles:r}=this.props;let o=r.overlay;return Nr()&&(o="center"===n?r.overlayLegacyCenter:r.overlayLegacy),{cursor:t?"default":"pointer",height:yr(),pointerEvents:e?"none":"auto",...o}}get spotlightStyles(){var e,t,n;const{showSpotlight:r}=this.state,{disableScrollParentFix:o=!1,spotlightClicks:i,spotlightPadding:s=0,styles:a,target:l}=this.props,c=br(l),p=gr(c),u=xr(c),d=function(e,t,n){var r,o,i;const s=gr(e),a=vr(e,n),l=wr(e,n),c=xr(e);let p=0,u=null!=(r=null==s?void 0:s.top)?r:0;l&&c?u=(null!=(o=null==e?void 0:e.offsetTop)?o:0)-(null!=(i=null==a?void 0:a.scrollTop)?i:0):a instanceof HTMLElement&&(p=a.scrollTop,l||xr(e)||(u+=p),a.isSameNode(Sr())||(u+=Sr().scrollTop));return Math.floor(u-t)}(c,s,o);return{...Nr()?a.spotlightLegacy:a.spotlight,height:Math.round((null!=(e=null==p?void 0:p.height)?e:0)+2*s),left:Math.round((null!=(t=null==p?void 0:p.left)?t:0)-s),opacity:r?1:0,pointerEvents:i?"none":"auto",position:u?"fixed":"absolute",top:d,transition:"opacity 0.2s",width:Math.round((null!=(n=null==p?void 0:p.width)?n:0)+2*s)}}updateState(e){this.isActive&&this.setState((t=>({...t,...e})))}render(){const{showSpotlight:e}=this.state,{onClickOverlay:t,placement:n}=this.props,{hideSpotlight:r,overlayStyles:i,spotlightStyles:s}=this;if(r())return null;let a="center"!==n&&e&&o.createElement(Kr,{styles:s});if("safari"===jr()){const{mixBlendMode:e,zIndex:t,...n}=i;a=o.createElement("div",{style:{...n}},a),delete i.backgroundColor}return o.createElement("div",{className:"react-joyride__overlay","data-test-id":"overlay",onClick:t,role:"presentation",style:i},a)}},Jr=class extends o.Component{constructor(){super(...arguments),$n(this,"node",null)}componentDidMount(){const{id:e}=this.props;mr()&&(this.node=document.createElement("div"),this.node.id=e,document.body.appendChild(this.node),Er||this.renderReact15())}componentDidUpdate(){mr()&&(Er||this.renderReact15())}componentWillUnmount(){mr()&&this.node&&(Er||i.unmountComponentAtNode(this.node),this.node.parentNode===document.body&&(document.body.removeChild(this.node),this.node=null))}renderReact15(){if(!mr())return;const{children:e}=this.props;this.node&&i.unstable_renderSubtreeIntoContainer(this,e,this.node)}renderReact16(){if(!mr()||!Er)return null;const{children:e}=this.props;return this.node?i.createPortal(e,this.node):null}render(){return Er?this.renderReact16():null}},Qr=class{constructor(e,t){if($n(this,"element"),$n(this,"options"),$n(this,"canBeTabbed",(e=>{const{tabIndex:t}=e;return!(null===t||t<0)&&this.canHaveFocus(e)})),$n(this,"canHaveFocus",(e=>{const t=e.nodeName.toLowerCase();return(/input|select|textarea|button|object/.test(t)&&!e.getAttribute("disabled")||"a"===t&&!!e.getAttribute("href"))&&this.isVisible(e)})),$n(this,"findValidTabElements",(()=>[].slice.call(this.element.querySelectorAll("*"),0).filter(this.canBeTabbed))),$n(this,"handleKeyDown",(e=>{const{code:t="Tab"}=this.options;e.code===t&&this.interceptTab(e)})),$n(this,"interceptTab",(e=>{e.preventDefault();const t=this.findValidTabElements(),{shiftKey:n}=e;if(!t.length)return;let r=document.activeElement?t.indexOf(document.activeElement):0;-1===r||!n&&r+1===t.length?r=0:n&&0===r?r=t.length-1:r+=n?-1:1,t[r].focus()})),$n(this,"isHidden",(e=>{const t=e.offsetWidth<=0&&e.offsetHeight<=0,n=window.getComputedStyle(e);return!(!t||e.innerHTML)||(t&&"visible"!==n.getPropertyValue("overflow")||"none"===n.getPropertyValue("display"))})),$n(this,"isVisible",(e=>{let t=e;for(;t;)if(t instanceof HTMLElement){if(t===document.body)break;if(this.isHidden(t))return!1;t=t.parentNode}return!0})),$n(this,"removeScope",(()=>{window.removeEventListener("keydown",this.handleKeyDown)})),$n(this,"checkFocus",(e=>{document.activeElement!==e&&(e.focus(),window.requestAnimationFrame((()=>this.checkFocus(e))))})),$n(this,"setFocus",(()=>{const{selector:e}=this.options;if(!e)return;const t=this.element.querySelector(e);t&&window.requestAnimationFrame((()=>this.checkFocus(t)))})),!(e instanceof HTMLElement))throw new TypeError("Invalid parameter: element must be an HTMLElement");this.element=e,this.options=t,window.addEventListener("keydown",this.handleKeyDown,!1),this.setFocus()}},Zr=class extends o.Component{constructor(e){if(super(e),$n(this,"beacon",null),$n(this,"setBeaconRef",(e=>{this.beacon=e})),e.beaconComponent)return;const t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.id="joyride-beacon-animation",e.nonce&&n.setAttribute("nonce",e.nonce);n.appendChild(document.createTextNode("\n        @keyframes joyride-beacon-inner {\n          20% {\n            opacity: 0.9;\n          }\n        \n          90% {\n            opacity: 0.7;\n          }\n        }\n        \n        @keyframes joyride-beacon-outer {\n          0% {\n            transform: scale(1);\n          }\n        \n          45% {\n            opacity: 0.7;\n            transform: scale(0.75);\n          }\n        \n          100% {\n            opacity: 0.9;\n            transform: scale(1);\n          }\n        }\n      ")),t.appendChild(n)}componentDidMount(){const{shouldFocus:e}=this.props;setTimeout((()=>{fe.domElement(this.beacon)&&e&&this.beacon.focus()}),0)}componentWillUnmount(){const e=document.getElementById("joyride-beacon-animation");(null==e?void 0:e.parentNode)&&e.parentNode.removeChild(e)}render(){const{beaconComponent:e,continuous:t,index:n,isLastStep:r,locale:i,onClickOrHover:s,size:a,step:l,styles:c}=this.props,p=Cr(i.open),u={"aria-label":p,onClick:s,onMouseEnter:s,ref:this.setBeaconRef,title:p};let d;if(e){const i=e;d=o.createElement(i,{continuous:t,index:n,isLastStep:r,size:a,step:l,...u})}else d=o.createElement("button",{key:"JoyrideBeacon",className:"react-joyride__beacon","data-test-id":"button-beacon",style:c.beacon,type:"button",...u},o.createElement("span",{style:c.beaconInner}),o.createElement("span",{style:c.beaconOuter}));return d}};var eo=function({styles:e,...n}){const{color:r,height:o,width:i,...s}=e;return t.createElement("button",{style:s,type:"button",...n},t.createElement("svg",{height:"number"==typeof o?`${o}px`:o,preserveAspectRatio:"xMidYMid",version:"1.1",viewBox:"0 0 18 18",width:"number"==typeof i?`${i}px`:i,xmlns:"http://www.w3.org/2000/svg"},t.createElement("g",null,t.createElement("path",{d:"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z",fill:r}))))};var to=function(e){const{backProps:t,closeProps:n,index:r,isLastStep:i,primaryProps:s,skipProps:a,step:l,tooltipProps:c}=e,{content:p,hideBackButton:u,hideCloseButton:d,hideFooter:f,showSkipButton:h,styles:m,title:g}=l,y={};return y.primary=o.createElement("button",{"data-test-id":"button-primary",style:m.buttonNext,type:"button",...s}),h&&!i&&(y.skip=o.createElement("button",{"aria-live":"off","data-test-id":"button-skip",style:m.buttonSkip,type:"button",...a})),!u&&r>0&&(y.back=o.createElement("button",{"data-test-id":"button-back",style:m.buttonBack,type:"button",...t})),y.close=!d&&o.createElement(eo,{"data-test-id":"button-close",styles:m.buttonClose,...n}),o.createElement("div",{key:"JoyrideTooltip","aria-label":Cr(null!=g?g:p),className:"react-joyride__tooltip",style:m.tooltip,...c},o.createElement("div",{style:m.tooltipContainer},g&&o.createElement("h1",{"aria-label":Cr(g),style:m.tooltipTitle},g),o.createElement("div",{style:m.tooltipContent},p)),!f&&o.createElement("div",{style:m.tooltipFooter},o.createElement("div",{style:m.tooltipFooterSpacer},y.skip),y.back,y.primary),y.close)},no=class extends o.Component{constructor(){super(...arguments),$n(this,"handleClickBack",(e=>{e.preventDefault();const{helpers:t}=this.props;t.prev()})),$n(this,"handleClickClose",(e=>{e.preventDefault();const{helpers:t}=this.props;t.close("button_close")})),$n(this,"handleClickPrimary",(e=>{e.preventDefault();const{continuous:t,helpers:n}=this.props;t?n.next():n.close("button_primary")})),$n(this,"handleClickSkip",(e=>{e.preventDefault();const{helpers:t}=this.props;t.skip()})),$n(this,"getElementsProps",(()=>{const{continuous:e,index:t,isLastStep:n,setTooltipRef:r,size:o,step:i}=this.props,{back:s,close:a,last:l,next:c,nextLabelWithProgress:p,skip:u}=i.locale,d=Cr(s),f=Cr(a),h=Cr(l),m=Cr(c),g=Cr(u);let y=a,b=f;if(e){if(y=c,b=m,i.showProgress&&!n){const e=Cr(p,{step:t+1,steps:o});y=Rr(p,t+1,o),b=e}n&&(y=l,b=h)}return{backProps:{"aria-label":d,children:s,"data-action":"back",onClick:this.handleClickBack,role:"button",title:d},closeProps:{"aria-label":f,children:a,"data-action":"close",onClick:this.handleClickClose,role:"button",title:f},primaryProps:{"aria-label":b,children:y,"data-action":"primary",onClick:this.handleClickPrimary,role:"button",title:b},skipProps:{"aria-label":g,children:u,"data-action":"skip",onClick:this.handleClickSkip,role:"button",title:g},tooltipProps:{"aria-modal":!0,ref:r,role:"alertdialog"}}}))}render(){const{continuous:e,index:t,isLastStep:n,setTooltipRef:r,size:i,step:s}=this.props,{beaconComponent:a,tooltipComponent:l,...c}=s;let p;if(l){const s={...this.getElementsProps(),continuous:e,index:t,isLastStep:n,size:i,step:c,setTooltipRef:r},a=l;p=o.createElement(a,{...s})}else p=o.createElement(to,{...this.getElementsProps(),continuous:e,index:t,isLastStep:n,size:i,step:s});return p}},ro=class extends o.Component{constructor(){super(...arguments),$n(this,"scope",null),$n(this,"tooltip",null),$n(this,"handleClickHoverBeacon",(e=>{const{step:t,store:n}=this.props;"mouseenter"===e.type&&"hover"!==t.event||n.update({lifecycle:fr.TOOLTIP})})),$n(this,"setTooltipRef",(e=>{this.tooltip=e})),$n(this,"setPopper",((e,t)=>{var n;const{action:r,lifecycle:o,step:i,store:s}=this.props;"wrapper"===t?s.setPopper("beacon",e):s.setPopper("tooltip",e),s.getPopper("beacon")&&(s.getPopper("tooltip")||"center"===i.placement)&&o===fr.INIT&&s.update({action:r,lifecycle:fr.READY}),(null==(n=i.floaterProps)?void 0:n.getPopper)&&i.floaterProps.getPopper(e,t)})),$n(this,"renderTooltip",(e=>{const{continuous:t,helpers:n,index:r,size:i,step:s}=this.props;return o.createElement(no,{continuous:t,helpers:n,index:r,isLastStep:r+1===i,setTooltipRef:this.setTooltipRef,size:i,step:s,...e})}))}componentDidMount(){const{debug:e,index:t}=this.props;Ir({title:`step:${t}`,data:[{key:"props",value:this.props}],debug:e})}componentDidUpdate(e){var t;const{action:n,callback:r,continuous:o,controlled:i,debug:s,helpers:a,index:l,lifecycle:c,shouldScroll:p,status:u,step:d,store:f}=this.props,{changed:h,changedFrom:m}=Se(e,this.props),g=a.info(),y=o&&n!==nr&&(l>0||n===Zn),b=h("action")||h("index")||h("lifecycle")||h("status"),v=m("lifecycle",[fr.TOOLTIP,fr.INIT],fr.INIT),w=h("action",[er,Zn,rr,nr]),x=i&&l===e.index;if(w&&(v||x)&&r({...g,index:e.index,lifecycle:fr.COMPLETE,step:e.step,type:cr}),"center"===d.placement&&u===hr.RUNNING&&h("index")&&n!==Xn&&c===fr.INIT&&f.update({lifecycle:fr.READY}),b){const e=br(d.target),t=!!e&&function(e){var t;if(!e)return!1;let n=e;for(;n&&n!==document.body;){if(n instanceof HTMLElement){const{display:e,visibility:t}=getComputedStyle(n);if("none"===e||"hidden"===t)return!1}n=null!=(t=n.parentElement)?t:null}return!0}(e);t?(m("status",hr.READY,hr.RUNNING)||m("lifecycle",fr.INIT,fr.READY))&&r({...g,step:d,type:sr}):(r({...g,type:dr,step:d}),i||f.update({index:l+(n===Zn?-1:1)}))}m("lifecycle",fr.INIT,fr.READY)&&f.update({lifecycle:Pr(d)||y?fr.TOOLTIP:fr.BEACON}),h("index")&&Ir({title:`step:${c}`,data:[{key:"props",value:this.props}],debug:s}),h("lifecycle",fr.BEACON)&&r({...g,step:d,type:ar}),h("lifecycle",fr.TOOLTIP)&&(r({...g,step:d,type:lr}),p&&this.tooltip&&(this.scope=new Qr(this.tooltip,{selector:"[data-action=primary]"}),this.scope.setFocus())),m("lifecycle",[fr.TOOLTIP,fr.INIT],fr.INIT)&&(null==(t=this.scope)||t.removeScope(),f.cleanupPoppers())}componentWillUnmount(){var e;null==(e=this.scope)||e.removeScope()}get open(){const{lifecycle:e,step:t}=this.props;return Pr(t)||e===fr.TOOLTIP}render(){const{continuous:e,debug:t,index:n,nonce:r,shouldScroll:i,size:s,step:a}=this.props,l=br(a.target);return Gr(a)&&fe.domElement(l)?o.createElement("div",{key:`JoyrideStep-${n}`,className:"react-joyride__step"},o.createElement(qn,{...a.floaterProps,component:this.renderTooltip,debug:t,getPopper:this.setPopper,id:`react-joyride-step-${n}`,open:this.open,placement:a.placement,target:a.target},o.createElement(Zr,{beaconComponent:a.beaconComponent,continuous:e,index:n,isLastStep:n+1===s,locale:a.locale,nonce:r,onClickOrHover:this.handleClickHoverBeacon,shouldFocus:i,size:s,step:a,styles:a.styles}))):null}},oo=class extends o.Component{constructor(e){super(e),$n(this,"helpers"),$n(this,"store"),$n(this,"callback",(e=>{const{callback:t}=this.props;fe.function(t)&&t(e)})),$n(this,"handleKeyboard",(e=>{const{index:t,lifecycle:n}=this.state,{steps:r}=this.props,o=r[t];n===fr.TOOLTIP&&"Escape"===e.code&&o&&!o.disableCloseOnEsc&&this.store.close("keyboard")})),$n(this,"handleClickOverlay",(()=>{const{index:e}=this.state,{steps:t}=this.props;Hr(this.props,t[e]).disableOverlayClose||this.helpers.close("overlay")})),$n(this,"syncState",(e=>{this.setState(e)}));const{debug:t,getHelpers:n,run:r=!0,stepIndex:o}=e;var i;this.store=(i={...e,controlled:r&&fe.number(o)},new $r(i)),this.helpers=this.store.getHelpers();const{addListener:s}=this.store;Ir({title:"init",data:[{key:"props",value:this.props},{key:"state",value:this.state}],debug:t}),s(this.syncState),n&&n(this.helpers),this.state=this.store.getState()}componentDidMount(){if(!mr())return;const{debug:e,disableCloseOnEsc:t,run:n,steps:r}=this.props,{start:o}=this.store;Ur(r,e)&&n&&o(),t||document.body.addEventListener("keydown",this.handleKeyboard,{passive:!0})}componentDidUpdate(e,t){if(!mr())return;const{action:n,controlled:r,index:o,status:i}=this.state,{debug:s,run:a,stepIndex:l,steps:c}=this.props,{stepIndex:p,steps:u}=e,{reset:d,setSteps:f,start:h,stop:m,update:g}=this.store,{changed:y}=Se(e,this.props),{changed:b,changedFrom:v}=Se(t,this.state),w=Hr(this.props,c[o]),x=!re(u,c),O=fe.number(l)&&y("stepIndex"),S=br(w.target);if(x&&Ur(c,s)&&f(c),y("run")&&(a?h(l):m()),O){let e=fe.number(p)&&p<l?er:Zn;n===Jn&&(e=Xn),[hr.FINISHED,hr.SKIPPED].includes(i)||g({action:n===nr?nr:e,index:l,lifecycle:fr.INIT})}r||i!==hr.RUNNING||0!==o||S||(this.store.update({index:o+1}),this.callback({...this.state,type:dr,step:w}));const E={...this.state,index:o,step:w};if(b("action",[er,Zn,rr,nr])&&b("status",hr.PAUSED)){const e=Hr(this.props,c[t.index]);this.callback({...E,index:t.index,lifecycle:fr.COMPLETE,step:e,type:cr})}if(b("status",[hr.FINISHED,hr.SKIPPED])){const e=Hr(this.props,c[t.index]);r||this.callback({...E,index:t.index,lifecycle:fr.COMPLETE,step:e,type:cr}),this.callback({...E,type:pr,step:e,index:t.index}),d()}else v("status",[hr.IDLE,hr.READY],hr.RUNNING)?this.callback({...E,type:ir}):(b("status")||b("action",Qn))&&this.callback({...E,type:ur});this.scrollToStep(t)}componentWillUnmount(){const{disableCloseOnEsc:e}=this.props;e||document.body.removeEventListener("keydown",this.handleKeyboard)}scrollToStep(e){const{index:t,lifecycle:n,status:r}=this.state,{debug:o,disableScrollParentFix:i=!1,scrollDuration:s,scrollOffset:a=20,scrollToFirstStep:l=!1,steps:c}=this.props,p=Hr(this.props,c[t]),u=br(p.target),d=function(e){const{isFirstStep:t,lifecycle:n,previousLifecycle:r,scrollToFirstStep:o,step:i,target:s}=e;return!i.disableScrolling&&(!t||o||n===fr.TOOLTIP)&&"center"!==i.placement&&(!i.isFixed||!xr(s))&&r!==n&&[fr.BEACON,fr.TOOLTIP].includes(n)}({isFirstStep:0===t,lifecycle:n,previousLifecycle:e.lifecycle,scrollToFirstStep:l,step:p,target:u});if(r===hr.RUNNING&&d){const e=wr(u,i),l=vr(u,i);let c=Math.floor(Or(u,a,i))||0;Ir({title:"scrollToStep",data:[{key:"index",value:t},{key:"lifecycle",value:n},{key:"status",value:r}],debug:o});const d=this.store.getPopper("beacon"),f=this.store.getPopper("tooltip");if(n===fr.BEACON&&d){const{offsets:t,placement:n}=d;["bottom"].includes(n)||e||(c=Math.floor(t.popper.top-a))}else if(n===fr.TOOLTIP&&f){const{flipped:t,offsets:n,placement:r}=f;!["top","right","left"].includes(r)||t||e?c-=p.spotlightPadding:c=Math.floor(n.popper.top-a)}c=c>=0?c:0,r===hr.RUNNING&&function(e,t){const{duration:n,element:r}=t;return new Promise(((t,o)=>{const{scrollTop:i}=r,s=e>i?e-i:i-e;je.top(r,e,{duration:s<100?50:n},(e=>e&&"Element already at target scroll position"!==e.message?o(e):t()))}))}(c,{element:l,duration:s}).then((()=>{setTimeout((()=>{var e;null==(e=this.store.getPopper("tooltip"))||e.instance.update()}),10)}))}}render(){if(!mr())return null;const{index:e,lifecycle:t,status:n}=this.state,{continuous:r=!1,debug:i=!1,nonce:s,scrollToFirstStep:a=!1,steps:l}=this.props,c={};if(n===hr.RUNNING&&l[e]){const n=Hr(this.props,l[e]);c.step=o.createElement(ro,{...this.state,callback:this.callback,continuous:r,debug:i,helpers:this.helpers,nonce:s,shouldScroll:!n.disableScrolling&&(0!==e||a),step:n,store:this.store}),c.overlay=o.createElement(Jr,{id:"react-joyride-portal"},o.createElement(Xr,{...n,continuous:r,debug:i,lifecycle:t,onClickOverlay:this.handleClickOverlay}))}return o.createElement("div",{className:"react-joyride"},c.step,c.overlay)}};$n(oo,"defaultProps",Dr);var io=oo;const so={"/stream":{mobile:[{target:".mobile-step-1",content:"Menus will appear here",placement:"right"},{target:".dashboard-step-3",content:"Displaying selected sensor name.",placement:"bottom"},{target:".dashboard-step-4",content:"Displaying sensor status live or replay",placement:"bottom"},{target:".dashboard-step-5",content:"Displaying show replay/live button.",placement:"left"},{target:".mobile-step-6",content:"Sensor details will be shown here, including current position, speed, and time (according to your local area).",placement:"left"},{target:".dashboard-step-7",content:"The list of sensors will be displayed here, along with their status (live/offline).",placement:"right"},{target:".dashboard-step-9",content:"The current position can be located on this map, which will refresh every 10 seconds",placement:"right"},{target:".dashboard-step-11",content:"The artifacts captured by the sensor within the last 24 hours will be displayed here.",placement:"top"}],desktop:[{target:".dashboard-step-1",content:"Displaying your username, profile picture, and a logout button.",placement:"left"},{target:".dashboard-step-2",content:"Menus will appear here",placement:"right"},{target:".dashboard-step-3",content:"Displaying selected sensor name.",placement:"bottom"},{target:".dashboard-step-4",content:"Displaying sensor status live or replay",placement:"bottom"},{target:".dashboard-step-5",content:"Click here to see Replays.",placement:"right"},{target:".dashboard-step-6",content:"Sensor details will be shown here, including current position, speed, and time (according to your local area).",placement:"left"},{target:".dashboard-step-7",content:"Click here to see Sensors.",placement:"right",spotlightClicks:!0},{target:".dashboard-step-8",content:"The list of sensors will be displayed here, along with their status (live/offline).",placement:"right"},{target:".dashboard-step-9",content:"Click here to see Map.",placement:"right",spotlightClicks:!0},{target:".dashboard-step-10",content:"The current position can be located on this map, which will refresh every 10 seconds.",placement:"right"},{target:".dashboard-step-11",content:"Click here to see Events.",placement:"right",spotlightClicks:!0},{target:".dashboard-step-12",content:"The artifacts captured by the sensor will be displayed here.",placement:"top"},{target:".dashboard-step-13",content:"The Replays options will be displayed here.",placement:"right"},{target:".dashboard-step-14",content:"This button will help you to save part of the video",placement:"right"},{target:".dashboard-step-15",content:"This button will help you to take screenshot from a current video",placement:"right"}]},"/map":{mobile:[{target:".map-step-2",content:"This panel allows users to view and interact with all available vessels on the map, providing options to control which vessels are displayed and focused on.",placement:"left"},{target:".map-step-4",content:"This panel allows users to refine the vessel history displayed on the map, offering flexible date and category filters as well as display options.",placement:"left"},{target:".map-step-6",content:"This panel allows users to fine-tune the display of data points to optimize performance and device load by adjusting the level of detail shown on the map.",placement:"left"}],desktop:[{target:".map-step-1",content:"Click here to see Vessels.",placement:"right",spotlightClicks:!0},{target:".map-step-2",content:"This panel allows users to view and interact with all available vessels on the map, providing options to control which vessels are displayed and focused on.",placement:"left"},{target:".map-step-3",content:"Click here to see Filters.",placement:"right",spotlightClicks:!0},{target:".map-step-4",content:"This panel allows users to refine the vessel history displayed on the map, offering flexible date and category filters as well as display options.",placement:"left"},{target:".map-step-5",content:"Click here to see Advanced Settings.",placement:"right",spotlightClicks:!0},{target:".map-step-6",content:"This panel allows users to fine-tune the display of data points to optimize performance and device load by adjusting the level of detail shown on the map.",placement:"left"}]},"/events":{mobile:[{target:".events-step-1",content:"You can filter events based on different parameters, Click here to see Filters.",placement:"bottom"},{target:".events-step-2",content:"This gives you a more detailed view about the artifacts, Click on an artifact to see more details.",placement:"top"}],desktop:[{target:".events-step-1",content:"You can filter events based on different parameters, Click here to see Filters.",placement:"bottom",spotlightClicks:!0},{target:".events-step-2",content:"This gives you a more detailed view about the artifacts, Click on an artifact to see more details.",placement:"left"}]},"/notification":{mobile:[{target:".notification-step-1",content:"Here you can switch between notifications and summary reports.",placement:"bottom"}],desktop:[{target:".notification-step-1",content:"Here you can switch between notifications and summary reports.",placement:"bottom"},{target:".notification-step-2",content:"Click here to create alerts.",placement:"bottom"},{target:".notification-step-3",content:"Click here to create summary reports.",placement:"bottom"}]}};function ao({isMobile:e}){const{pathname:t}=s(),[n,r]=o.useState(!1),[i,l]=o.useState(null),[,c]=o.useState(null),{localStorageKey:p,steps:u}=o.useMemo((()=>{for(const n in so)if(t.includes(n)){const t={target:"body",content:a.jsxs("div",{style:{textAlign:"center"},children:[a.jsx("h2",{children:"Welcome to Quartermaster"}),a.jsx("p",{children:"We are excited to give you a quick tour to show you around."})]}),placement:"center",disableBeacon:!0,isFixed:!0,styles:{options:{zIndex:1e4,width:"100%",height:"100%",overlayColor:"rgba(0, 0, 0, 0.9)"}}},r=e?[t,...so[n].mobile]:[t,...so[n].desktop];return{localStorageKey:n.includes("/stream")?"dashboardTourGuide":n.includes("/map")?"mapTourGuide":n.includes("/notification")?"notificationTourGuide":"eventsTourGuide",steps:r}}return{localStorageKey:"",steps:[]}}),[t,e]),d=async()=>{p&&!localStorage.getItem(p)&&(r(!0),l(0))};return o.useEffect((()=>{D.get("/tourGuides").then((e=>{if(0===e.data.length&&D.post("/tourGuides",{events:Boolean(localStorage.getItem("eventsTourGuide"))||!1,maps:Boolean(localStorage.getItem("mapTourGuide"))||!1,streams:Boolean(localStorage.getItem("dashboardTourGuide"))||!1,notifications:Boolean(localStorage.getItem("notificationTourGuide"))}).then((e=>{c(e.data)})).catch((e=>{})),e.data.length>0){c(e.data[0]),localStorage.getItem("mapTourGuide")||!0!==e.data[0].maps||localStorage.setItem("mapTourGuide","true"),localStorage.getItem("dashboardTourGuide")||!0!==e.data[0].streams||localStorage.setItem("dashboardTourGuide","true"),localStorage.getItem("eventsTourGuide")||!0!==e.data[0].events||localStorage.setItem("eventsTourGuide","true"),localStorage.getItem("notificationTourGuide")||!0!==e.data[0].notifications||localStorage.setItem("notificationTourGuide","true");const t={};"true"===localStorage.getItem("mapTourGuide")&&!1===e.data[0].maps&&(t.maps=!0),"true"===localStorage.getItem("dashboardTourGuide")&&!1===e.data[0].streams&&(t.streams=!0),"true"===localStorage.getItem("eventsTourGuide")&&!1===e.data[0].events&&(t.events=!0),"true"===localStorage.getItem("notificationTourGuide")&&!1===e.data[0].notifications&&(t.notifications=!0),Object.keys(t).length>0&&D.patch("/tourGuides/",t).then((e=>{c(e.data)})).catch((e=>{}))}})).catch((e=>{}));const e=setTimeout(d,5e3);return()=>clearTimeout(e)}),[t]),a.jsx(io,{steps:u,stepIndex:i,callback:n=>{const{status:o,index:i}=n;if(e&&i===u.length-1){let e;t.includes("/map")?e=document.querySelector(".map-step-3"):t.includes("/stream")?e=document.querySelector(".dashboard-step-9"):t.includes("/events")?e=document.querySelector(".events-step-4"):t.includes("/notification")&&(e=document.querySelector(".notification-step-4")),e&&e.scrollIntoView({behavior:"smooth",block:"center"})}if(p&&(o===hr.FINISHED||o===hr.SKIPPED)){localStorage.setItem(p,"true");const e={};"dashboardTourGuide"===p&&(e.streams=!0),"mapTourGuide"===p&&(e.maps=!0),"eventsTourGuide"===p&&(e.events=!0),"notificationTourGuide"===p&&(e.notifications=!0),D.patch("/tourGuides/",e).then((e=>{c(e.data)})),r(!1),l(0)}},run:n,continuous:!0,hideCloseButton:!0,scrollToFirstStep:!0,hideBackButton:!0,disableCloseOnEsc:!0,disableOverlayClose:!0,disableScrolling:!1,showProgress:!0,showSkipButton:!0,locale:{last:"✔"},styles:{options:{zIndex:1e4,primaryColor:"red",overlayColor:"rgba(0, 0, 0, 0.9)"},overlay:{backgroundColor:"rgba(0, 0, 0, 0.5)",mixBlendMode:"hard-light"},tooltip:{width:"300px"},buttonNext:{backgroundColor:"rgb(21, 25, 30)",padding:"10px",color:"#FFFFFF",border:"none",borderRadius:"10px"}}})}const lo=({logoutOnly:e,avatarOnly:t})=>{const{logout:n,user:r,timezone:i}=z(),s=l(),[C,T]=o.useState(null),P=Boolean(C),[N,I]=o.useState(null),F=Boolean(N),[R,L]=o.useState([]),[M,A]=o.useState(!1),[G,U]=o.useState(50),[V,q]=o.useState(1),[Y,$]=o.useState(!1),K=async()=>{$(!0);const{data:e}=await D.get(`/inAppNotifications?page=${V}&page_size=${G}`).then((e=>e.data)).catch(console.error),t=e.filter((e=>e.artifact_details)),n=t.some((e=>0==e.is_read));A(n);const r=R.concat(t).sort(((e,t)=>new Date(t.artifact_details.timestamp)-new Date(e.artifact_details.timestamp)));L(r),$(!1)};o.useEffect((()=>(X.on("in_app_notification",K),()=>X.off("in_app_notification",K))),[]),o.useEffect((()=>{K()}),[V]);const J=e=>{I(e.currentTarget)},Q=()=>{I(null)},Z=(e,t,n)=>{n.is_read||(async e=>{await D.patch(`/inAppNotifications/markRead/${e}`).catch(console.error)})(t),s(`/dashboard/events/${e}`),Q(),K()},ee=()=>{q(V+1)},te=()=>{T(null)},ne=()=>{n(),te()},re=()=>a.jsx(w,{onClick:ne,sx:{fontSize:"24px",color:"red",cursor:"pointer"}}),oe=()=>a.jsx(k,{sx:{width:"35px",height:"35px"}}),ie=()=>a.jsxs(a.Fragment,{children:[a.jsx(f,{onClick:J,sx:{fontSize:"30px",color:"#FFFFFF",cursor:"pointer",display:"flex"},children:a.jsx(x,{color:"error",variant:M?"dot":"standard",overlap:"circular",children:a.jsx(O,{})})}),a.jsx(g,{anchorEl:N,open:F,onClose:Q,anchorOrigin:{vertical:50,horizontal:-60},children:a.jsxs(c,{container:!0,sx:{width:{xs:"320px",lg:"400px"}},children:[a.jsxs(c,{width:"100%",container:!0,justifyContent:"space-between",alignItems:"center",sx:{backgroundColor:"primary.main",padding:1,marginTop:-1},children:[a.jsx(c,{children:a.jsx(d,{sx:{color:"white"},children:"Alerts"})}),a.jsx(c,{children:a.jsxs(f,{onClick:()=>(async()=>{const e=R.filter((e=>0==e.is_read));if(0===e.length||!e)return;const t=e.map((e=>e._id));await D.patch("/inAppNotifications/bulkMarkRead",{ids:t}).then((()=>{K()})).catch(console.error)})(),size:"small",disabled:!M,children:[a.jsx(d,{sx:{color:"white",paddingRight:"5px"},fontSize:"14px",fontWeight:300,children:"Mark all as read"}),a.jsx(S,{sx:{color:"white"}})]})})]}),R.length>0&&R.map(((e,t)=>a.jsxs(c,{width:"100%",container:!0,sx:{justifyContent:"space-between",alignItems:"center",padding:1,flexWrap:"nowrap",backgroundColor:e.is_read?"#343B44":"#464F59",cursor:"pointer"},onClick:()=>Z(e.artifact_details._id,e._id,e),children:[a.jsxs(c,{sx:{flex:1,overflow:"hidden"},children:[a.jsxs(d,{sx:{color:"white"},children:[a.jsx(d,{component:"span",fontSize:"14px",fontWeight:500,children:e.artifact_details.onboard_vessel_name}),a.jsxs(d,{component:"span",fontSize:"14px",fontWeight:300,children:[" ","has detected a vessel ",e.is_read]})]}),a.jsxs(d,{sx:{color:"white"},children:[a.jsxs(d,{component:"span",fontSize:"14px",fontWeight:300,children:["Category:"," "]}),a.jsx(d,{component:"span",fontSize:"14px",fontWeight:500,children:e.artifact_details.category})]}),a.jsxs(d,{sx:{color:"white"},children:[a.jsxs(d,{component:"span",fontSize:"14px",fontWeight:300,children:["Location:"," "]}),a.jsx(d,{component:"span",fontSize:"14px",fontWeight:500,children:_(e.artifact_details.location.coordinates,!!r?.use_MGRS)||""})]}),a.jsxs(d,{sx:{color:"white"},children:[a.jsxs(d,{component:"span",fontSize:"14px",fontWeight:300,children:["Detection Time:"," "]}),a.jsx(d,{component:"span",fontSize:"14px",fontWeight:500,children:B(e.artifact_details.timestamp).tz(i).format(H.dateTimeFormat(r,{exclude_seconds:!0}))})]})]}),a.jsx(c,{sx:{minWidth:60,textAlign:"right"},children:a.jsx("img",{src:e.artifact_details.thumbnail_url||e.artifact_details.image_url,loading:"lazy",alt:"Artifact",style:{borderRadius:"8px",maxWidth:"100px",height:"60px",objectFit:"cover"}})})]},t))),0===R.length?a.jsx(c,{container:!0,justifyContent:"center",width:"100%",children:a.jsx(d,{item:!0,sx:{color:"white"},children:"No Notification is Available"})}):a.jsx(c,{container:!0,justifyContent:"center",alignItems:"center",sx:{padding:1,width:"100%"},children:a.jsx(p,{width:"100%",display:"flex",justifyContent:"center",children:Y?a.jsx(E,{size:18,sx:{color:"white"}}):a.jsx(j,{variant:"text",sx:{color:"white"},size:"small",onClick:ee,children:"Load More"})})})]})})]});return e?a.jsx(re,{}):t?a.jsxs(c,{container:!0,children:[ie(),a.jsx(oe,{})]}):a.jsx(c,{container:!0,gap:"10px",children:a.jsxs(p,{className:"dashboard-step-1",sx:{padding:"6px 10px",borderRadius:20,backgroundColor:u(W.palette.background.default,.25),display:"flex",justifyContent:"space-between",alignItems:"center",gap:"10px"},children:[ie(),a.jsx(d,{color:"#FFFFFF",fontWeight:"bold",sx:{display:"inline",padding:"6px 15px",borderRadius:"20px",backgroundColor:"#FFFFFF",color:"#000",fontSize:"14px"},children:r?r.name:"Anonymous"}),a.jsx(oe,{}),a.jsx(f,{onClick:e=>{T(e.currentTarget)},sx:{fontSize:"24px",color:"#FFFFFF",cursor:"pointer"},children:C?a.jsx(h,{}):a.jsx(m,{})}),a.jsxs(g,{id:"basic-menu",anchorEl:C,open:P,onClose:te,MenuListProps:{"aria-labelledby":"basic-button"},sx:{"& .MuiList-root":{backgroundColor:"#4F5968"}},transformOrigin:{vertical:100,horizontal:"center"},anchorOrigin:{vertical:150,horizontal:"left"},children:[a.jsxs(y,{onClick:()=>{s("/dashboard/settings"),te()},sx:{minWidth:"250px",display:"flex",justifyContent:"space-between",backgroundColor:"#4F5968 !important"},children:["Settings",a.jsx(b,{sx:{fontSize:"24px",color:"#FFFFFF",cursor:"pointer"}})]}),a.jsx(v,{variant:"middle",sx:{backgroundColor:u("#FFFFFF",.3)},component:"li"}),a.jsxs(y,{onClick:ne,sx:{minWidth:"250px",display:"flex",justifyContent:"space-between",backgroundColor:"#4F5968 !important"},children:["Logout",a.jsx(re,{})]})]})]})})},co=({setDrawerOpen:e})=>{const{screenSize:t}=K();return a.jsxs(c,{container:!0,bgcolor:"primary.main",flexDirection:{xs:"row",sm:"row"},justifyContent:"space-between",alignItems:"center",paddingX:2,paddingY:1,children:[a.jsx(c,{display:{xs:"flex",lg:"none"},className:"mobile-step-1",children:a.jsx(f,{onClick:()=>e(!0),sx:{color:"primary.contrastText",padding:0},disableRipple:!0,children:a.jsx(C,{})})}),a.jsxs(c,{container:!0,display:"flex",alignItems:"center",gap:{xs:0,lg:2},flexDirection:{xs:"column-reverse",lg:"row"},size:{xs:"auto",lg:"grow"},children:[a.jsxs(c,{display:"flex",alignItems:"flex-end",children:[a.jsx(d,{color:"#FFFFFF",fontWeight:"bold",sx:{fontSize:{sm:"18px",lg:"2.0243rem"}},children:"SMARTMAST"}),a.jsxs(d,{color:"#FFFFFF",fontWeight:"400",sx:{fontSize:{sm:"12px",lg:"14px"},lineHeight:{xs:"21px",lg:"35px"}},children:["v",G]})]}),a.jsx(c,{container:!0,display:"flex",alignItems:"center",gap:{xs:1,sm:2},marginLeft:"auto",marginRight:"auto",size:"auto",children:a.jsxs(c,{display:"flex",justifyContent:"center",alignItems:"center",gap:"10px",children:[a.jsx("img",{src:"/quartermaster-logo-white.svg",alt:"Quartermaster Logo",width:30}),a.jsx("img",{src:"/quartermaster-text-white.svg",alt:"Quartermaster Logo",width:t.xs?180:230})]})})]}),a.jsx(c,{children:a.jsx(lo,{avatarOnly:t.xs||t.sm||t.md})})]})},po=({drawerOpen:e,setDrawerOpen:t})=>{const{screenSize:n}=K(),{user:r}=z(),i=l(),{pathname:p}=s(),[u,f]=o.useState(!0),[h,m]=o.useState(!1),g=e=>{t&&t(!1),i(e)};o.useEffect((()=>{const e=U(),t=()=>f(!0),n=()=>f(!1);return e.on("connect",t),e.on("disconnect",n),()=>{e.off("connect",t),e.off("disconnect",n)}}),[]);const y=[{label:"Streams",icon:a.jsx(T,{}),path:"/dashboard/stream",isVisible:!0},{label:"Events",icon:a.jsx("img",{src:"/icons/event-icon.svg",alt:"Events Icon",style:{width:"24px",height:"24px"}}),path:"/dashboard/events",isVisible:!0},{label:"Map",icon:a.jsx(P,{}),path:"/dashboard/map",isVisible:!0},{label:"Users",icon:a.jsx(N,{}),path:"/dashboard/users",permissions:[V.manageUsers,V.manageRoles],isVisible:r?.hasPermissions([V.manageUsers,V.manageRoles],"OR")},{label:"Logs",icon:a.jsx("img",{src:"/icons/logs-icon.svg",alt:"Logs Icon",style:{width:"24px",height:"24px"}}),path:"/dashboard/logs",permissions:[V.viewSessionLogs],isVisible:r?.hasPermissions([V.viewSessionLogs])},{label:"Api Keys",icon:a.jsx("img",{src:"/icons/api-keys-icon.svg",alt:"Api Key Icon",style:{width:"24px",height:"24px"}}),path:"/dashboard/api-keys",isVisible:r?.hasPermissions([V.manageApiKeys])},{label:"Stats",icon:a.jsx("img",{src:"/icons/stats-icon.svg",alt:"Stats Icon",style:{width:"24px",height:"24px"}}),path:"/dashboard/statistics",isVisible:r?.hasPermissions([V.viewStatistics])},{label:"Alerts",icon:a.jsx(O,{}),path:"/dashboard/notification",isVisible:r?.hasPermissions([V.manageNotificationsAlerts])||!1},{label:"Vessels",icon:a.jsx(I,{}),path:"/dashboard/vessel-management",isVisible:r?.hasPermissions([V.manageVessels,V.manageRegionsGroups],"OR")}],v=[{icon:a.jsx(b,{onClick:()=>g("/dashboard/settings"),sx:{fontSize:"24px",color:"#FFFFFF",cursor:"pointer"}}),label:"Settings",path:"/dashboard/settings"},{icon:a.jsx(lo,{logoutOnly:n.xs||n.sm||n.md}),label:"Logout"}];return a.jsxs(c,{container:!0,className:"dashboard-step-2",flexDirection:"column",height:"auto",sx:{backgroundColor:"primary.main"},color:W.palette.custom.unfocused,padding:{xs:.5,lg:1},paddingY:{xs:.5,lg:1},gap:3,flexWrap:"nowrap",overflow:"auto",children:[y.map(((t,n)=>t.isVisible&&a.jsxs(c,{container:!0,onClick:t.path?()=>g(t.path):t.onClick,gap:2,padding:1,justifyContent:"flex-start",alignItems:"center",color:"primary.contrastText",flexWrap:"nowrap",sx:{transition:"width 0.5s, background-color 0.5s",backgroundColor:p===t.path?W.palette.custom.mainBlue:"transparent",borderRadius:"5px",overflow:"hidden",width:e||h?"150px !important":"40px !important",cursor:"pointer"},size:"auto",children:[a.jsx(c,{display:"flex",alignItems:"center",children:t.icon}),t.label&&a.jsx(c,{sx:{textWrap:"nowrap",opacity:e||h?1:0,transition:"opacity 0.5s, transform 0.5s !important",transform:e||h?"translateX(0)":"translateX(-10px)"},children:a.jsx(d,{fontSize:"12px",children:t.label})})]},n))),a.jsxs(c,{container:!0,flexDirection:"column",gap:1,alignItems:"center",marginTop:"auto",children:[a.jsx(c,{display:u?"none":"block",children:a.jsx(F,{color:"warning",fontSize:"large"})}),a.jsx(c,{container:!0,alignItems:"center",flexDirection:"column",display:{xs:"flex",lg:"none"},gap:3,children:v.map(((t,n)=>a.jsxs(c,{container:!0,onClick:t.path?()=>g(t.path):t.onClick,gap:2,padding:1,justifyContent:"flex-start",alignItems:"center",color:"primary.contrastText",sx:{transition:"0.2s",backgroundColor:p===t.path?W.palette.custom.mainBlue:"transparent",borderRadius:"5px",minWidth:e||h?"150px":"0",":hover":{color:"primary.contrastText",cursor:"pointer",transition:"0.2s"}},size:"auto",children:[a.jsx(c,{display:"flex",alignItems:"center",children:t.icon}),(e||h)&&t.label&&a.jsx(c,{children:a.jsx(d,{fontSize:"12px",children:t.label})})]},n)))}),!e&&a.jsx(c,{container:!0,onClick:()=>m((e=>!e)),gap:2,padding:1,justifyContent:"flex-end",alignItems:"center",color:"primary.contrastText",flexWrap:"nowrap",sx:{transition:"width 0.5s, background-color 0.5s",borderRadius:"5px",overflow:"hidden",width:e||h?"150px !important":"40px !important",cursor:"pointer"},size:"auto",children:a.jsx(R,{sx:{transform:h?"rotate(270deg)":"rotate(90deg)"}})})]})]})},uo=/^\/dashboard\/events\/[a-fA-F0-9]+$/;function fo(){const{screenSize:e,isMobile:t}=K(),{fetchUser:n,logout:r,user:i}=z(),p=l(),{pathname:u}=s(),d=o.useRef(null),[f,h]=o.useState(!1),m=o.useRef(Date.now()),g=s();o.useEffect((()=>{window.gtag&&i&&(window.gtag("set",{user_id:i._id}),window.gtag("set","user_properties",{user_name:i.name}))}),[i]),o.useEffect((()=>(q("page_view",{page_title:"Quartermaster page"+g.search,page_location:g.search}),()=>{if(i){const e=Math.round((Date.now()-m.current)/1e3);q("time_on_page",{page_path:window.location.pathname||g.pathname,user_id:i._id,user_name:i.name,duration_seconds:e})}})),[g]),o.useEffect((()=>{const e=U();uo.test(u)&&sessionStorage.setItem("eventPath",u);const t=async()=>{await J.getUserFlaggedArtifactIds()};return n().then((()=>t())).catch(console.error),$((()=>{r((()=>p("/login")))})),e.on("artifacts_flagged/changed",t),()=>{e.off("artifacts_flagged/changed",t)}}),[]),o.useEffect((()=>{f&&(e.xs||e.sm||e.md||h(!1))}),[e]);return(e=>{if(!e)return!0;try{const t=Y(e),n=Date.now()/1e3;return t.exp<n}catch(t){return!0}})(localStorage.getItem("jwt_token"))?a.jsx(L,{to:"/login"}):a.jsxs(c,{container:!0,height:"100vh",flexDirection:"column",bgcolor:"black",children:[a.jsx(ao,{isMobile:t}),a.jsx(c,{ref:d,size:"auto",children:a.jsx(co,{setDrawerOpen:h})}),a.jsxs(c,{container:!0,flexDirection:"row",overflow:"auto",flexWrap:"nowrap",size:"grow",children:[a.jsx(c,{display:{xs:"none",lg:"flex"},children:a.jsx(po,{})}),a.jsx(c,{minHeight:{xs:"auto",lg:"auto"},height:{xs:"auto",lg:"100%"},size:"grow",children:a.jsx(M,{})})]}),a.jsx(c,{display:{xs:"flex",lg:"none"},children:a.jsx(A,{open:f,onClose:()=>h(!1),children:a.jsx(po,{drawerOpen:f,setDrawerOpen:h})})})]})}export{fo as default};
