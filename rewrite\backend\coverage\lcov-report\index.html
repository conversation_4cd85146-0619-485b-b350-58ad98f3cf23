
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">97.07% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>5375/5537</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">91.23% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1613/1768</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">96.62% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>887/918</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">97.46% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>4997/5127</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="92.95" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.95" class="pct high">92.95%</td>
	<td data-value="71" class="abs high">66/71</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="6" class="abs medium">3/6</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="10" class="abs high">8/10</td>
	<td data-value="95.45" class="pct high">95.45%</td>
	<td data-value="66" class="abs high">63/66</td>
	</tr>

<tr>
	<td class="file high" data-value="src/middlewares"><a href="src/middlewares/index.html">src/middlewares</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="104" class="abs high">104/104</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="38" class="abs high">38/38</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="89" class="abs high">89/89</td>
	</tr>

<tr>
	<td class="file high" data-value="src/models"><a href="src/models/index.html">src/models</a></td>
	<td data-value="95.08" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 95%"></div><div class="cover-empty" style="width: 5%"></div></div>
	</td>
	<td data-value="95.08" class="pct high">95.08%</td>
	<td data-value="244" class="abs high">232/244</td>
	<td data-value="80.76" class="pct high">80.76%</td>
	<td data-value="26" class="abs high">21/26</td>
	<td data-value="73.17" class="pct medium">73.17%</td>
	<td data-value="41" class="abs medium">30/41</td>
	<td data-value="96.61" class="pct high">96.61%</td>
	<td data-value="236" class="abs high">228/236</td>
	</tr>

<tr>
	<td class="file high" data-value="src/modules"><a href="src/modules/index.html">src/modules</a></td>
	<td data-value="86.95" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.95" class="pct high">86.95%</td>
	<td data-value="705" class="abs high">613/705</td>
	<td data-value="75.09" class="pct medium">75.09%</td>
	<td data-value="261" class="abs medium">196/261</td>
	<td data-value="93.42" class="pct high">93.42%</td>
	<td data-value="76" class="abs high">71/76</td>
	<td data-value="86.35" class="pct high">86.35%</td>
	<td data-value="667" class="abs high">576/667</td>
	</tr>

<tr>
	<td class="file high" data-value="src/queries"><a href="src/queries/index.html">src/queries</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	</tr>

<tr>
	<td class="file high" data-value="src/routes"><a href="src/routes/index.html">src/routes</a></td>
	<td data-value="98.55" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 98%"></div><div class="cover-empty" style="width: 2%"></div></div>
	</td>
	<td data-value="98.55" class="pct high">98.55%</td>
	<td data-value="3109" class="abs high">3064/3109</td>
	<td data-value="91.77" class="pct high">91.77%</td>
	<td data-value="900" class="abs high">826/900</td>
	<td data-value="98.05" class="pct high">98.05%</td>
	<td data-value="567" class="abs high">556/567</td>
	<td data-value="99.13" class="pct high">99.13%</td>
	<td data-value="2885" class="abs high">2860/2885</td>
	</tr>

<tr>
	<td class="file high" data-value="src/routes/v2"><a href="src/routes/v2/index.html">src/routes/v2</a></td>
	<td data-value="97.68" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.68" class="pct high">97.68%</td>
	<td data-value="302" class="abs high">295/302</td>
	<td data-value="92.7" class="pct high">92.7%</td>
	<td data-value="96" class="abs high">89/96</td>
	<td data-value="95" class="pct high">95%</td>
	<td data-value="40" class="abs high">38/40</td>
	<td data-value="98.95" class="pct high">98.95%</td>
	<td data-value="287" class="abs high">284/287</td>
	</tr>

<tr>
	<td class="file high" data-value="src/services"><a href="src/services/index.html">src/services</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="529" class="abs high">529/529</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="200" class="abs high">200/200</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="96" class="abs high">96/96</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="474" class="abs high">474/474</td>
	</tr>

<tr>
	<td class="file high" data-value="src/utils"><a href="src/utils/index.html">src/utils</a></td>
	<td data-value="99.78" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 99%"></div><div class="cover-empty" style="width: 1%"></div></div>
	</td>
	<td data-value="99.78" class="pct high">99.78%</td>
	<td data-value="464" class="abs high">463/464</td>
	<td data-value="99.57" class="pct high">99.57%</td>
	<td data-value="237" class="abs high">236/237</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="73" class="abs high">73/73</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="415" class="abs high">415/415</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-10-08T16:06:53.102Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    