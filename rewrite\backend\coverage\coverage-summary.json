{"total": {"lines": {"total": 5127, "covered": 4997, "skipped": 0, "pct": 97.46}, "statements": {"total": 5537, "covered": 5375, "skipped": 0, "pct": 97.07}, "functions": {"total": 918, "covered": 887, "skipped": 0, "pct": 96.62}, "branches": {"total": 1768, "covered": 1613, "skipped": 0, "pct": 91.23}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\server.ts": {"lines": {"total": 66, "covered": 63, "skipped": 0, "pct": 95.45}, "functions": {"total": 10, "covered": 8, "skipped": 0, "pct": 80}, "statements": {"total": 71, "covered": 66, "skipped": 0, "pct": 92.95}, "branches": {"total": 6, "covered": 3, "skipped": 0, "pct": 50}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\assignEndpointId.ts": {"lines": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\auth.ts": {"lines": {"total": 38, "covered": 38, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 49, "covered": 49, "skipped": 0, "pct": 100}, "branches": {"total": 19, "covered": 19, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\hasPermission.ts": {"lines": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}, "functions": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "statements": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}, "branches": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\multerConfig.ts": {"lines": {"total": 18, "covered": 18, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 19, "covered": 19, "skipped": 0, "pct": 100}, "branches": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\restrictEndpointByUser.ts": {"lines": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}, "branches": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\validator.ts": {"lines": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}, "functions": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "statements": {"total": 10, "covered": 10, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ApiEndpoint.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ApiKey.ts": {"lines": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ArtifactFavourites.ts": {"lines": {"total": 14, "covered": 14, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ArtifactFlag.ts": {"lines": {"total": 17, "covered": 17, "skipped": 0, "pct": 100}, "functions": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "statements": {"total": 19, "covered": 19, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ArtifactSuggestion.ts": {"lines": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ArtifactSynonym.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\EmailDomains.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Geolocation.ts": {"lines": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\HomePort.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\InAppNotification.ts": {"lines": {"total": 7, "covered": 7, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 7, "covered": 7, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\InviteToken.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\NotificationAlert.ts": {"lines": {"total": 9, "covered": 6, "skipped": 0, "pct": 66.66}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 10, "covered": 6, "skipped": 0, "pct": 60}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\NotificationSummary.ts": {"lines": {"total": 9, "covered": 6, "skipped": 0, "pct": 66.66}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 10, "covered": 6, "skipped": 0, "pct": 60}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Organization.ts": {"lines": {"total": 7, "covered": 6, "skipped": 0, "pct": 85.71}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 7, "covered": 6, "skipped": 0, "pct": 85.71}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Permission.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Region.ts": {"lines": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\RegionGroup.ts": {"lines": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}, "branches": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Role.ts": {"lines": {"total": 22, "covered": 21, "skipped": 0, "pct": 95.45}, "functions": {"total": 6, "covered": 4, "skipped": 0, "pct": 66.66}, "statements": {"total": 24, "covered": 21, "skipped": 0, "pct": 87.5}, "branches": {"total": 4, "covered": 3, "skipped": 0, "pct": 75}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\SessionLog.ts": {"lines": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}, "functions": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "statements": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Statistics.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ThingsboardDevices.ts": {"lines": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\TourGuide.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\User.ts": {"lines": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "functions": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "statements": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\UserCompletionLogs.ts": {"lines": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Vessel.ts": {"lines": {"total": 34, "covered": 34, "skipped": 0, "pct": 100}, "functions": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "statements": {"total": 34, "covered": 34, "skipped": 0, "pct": 100}, "branches": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\awsKinesis.ts": {"lines": {"total": 289, "covered": 223, "skipped": 0, "pct": 77.16}, "functions": {"total": 22, "covered": 20, "skipped": 0, "pct": 90.9}, "statements": {"total": 291, "covered": 225, "skipped": 0, "pct": 77.31}, "branches": {"total": 120, "covered": 77, "skipped": 0, "pct": 64.16}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\awsS3.ts": {"lines": {"total": 225, "covered": 200, "skipped": 0, "pct": 88.88}, "functions": {"total": 17, "covered": 14, "skipped": 0, "pct": 82.35}, "statements": {"total": 229, "covered": 203, "skipped": 0, "pct": 88.64}, "branches": {"total": 101, "covered": 79, "skipped": 0, "pct": 78.21}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\db.ts": {"lines": {"total": 24, "covered": 24, "skipped": 0, "pct": 100}, "functions": {"total": 18, "covered": 18, "skipped": 0, "pct": 100}, "statements": {"total": 42, "covered": 42, "skipped": 0, "pct": 100}, "branches": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\email.ts": {"lines": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\geolocation.ts": {"lines": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "branches": {"total": 7, "covered": 7, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\ioEmitter.ts": {"lines": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\microservice_socket.ts": {"lines": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\otpService.ts": {"lines": {"total": 40, "covered": 40, "skipped": 0, "pct": 100}, "functions": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}, "statements": {"total": 49, "covered": 49, "skipped": 0, "pct": 100}, "branches": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\pLimit.ts": {"lines": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\processLogs.ts": {"lines": {"total": 15, "covered": 15, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "branches": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\spellingCorrector.ts": {"lines": {"total": 27, "covered": 27, "skipped": 0, "pct": 100}, "functions": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "statements": {"total": 27, "covered": 27, "skipped": 0, "pct": 100}, "branches": {"total": 10, "covered": 10, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\swagger.ts": {"lines": {"total": 14, "covered": 14, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "branches": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\queries\\User.ts": {"lines": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "functions": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "statements": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}, "branches": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ApiEndpoint.route.ts": {"lines": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ApiKey.route.ts": {"lines": {"total": 79, "covered": 79, "skipped": 0, "pct": 100}, "functions": {"total": 26, "covered": 26, "skipped": 0, "pct": 100}, "statements": {"total": 84, "covered": 84, "skipped": 0, "pct": 100}, "branches": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Artifact.route.ts": {"lines": {"total": 382, "covered": 378, "skipped": 0, "pct": 98.95}, "functions": {"total": 81, "covered": 78, "skipped": 0, "pct": 96.29}, "statements": {"total": 417, "covered": 407, "skipped": 0, "pct": 97.6}, "branches": {"total": 125, "covered": 117, "skipped": 0, "pct": 93.6}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ArtifactCompletions.route.ts": {"lines": {"total": 134, "covered": 131, "skipped": 0, "pct": 97.76}, "functions": {"total": 19, "covered": 19, "skipped": 0, "pct": 100}, "statements": {"total": 141, "covered": 138, "skipped": 0, "pct": 97.87}, "branches": {"total": 78, "covered": 55, "skipped": 0, "pct": 70.51}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ArtifactFavourites.route.ts": {"lines": {"total": 20, "covered": 20, "skipped": 0, "pct": 100}, "functions": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "statements": {"total": 20, "covered": 20, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ArtifactSuggestions.route.ts": {"lines": {"total": 95, "covered": 95, "skipped": 0, "pct": 100}, "functions": {"total": 26, "covered": 26, "skipped": 0, "pct": 100}, "statements": {"total": 107, "covered": 105, "skipped": 0, "pct": 98.13}, "branches": {"total": 27, "covered": 25, "skipped": 0, "pct": 92.59}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Audio.route.ts": {"lines": {"total": 68, "covered": 65, "skipped": 0, "pct": 95.58}, "functions": {"total": 11, "covered": 10, "skipped": 0, "pct": 90.9}, "statements": {"total": 73, "covered": 69, "skipped": 0, "pct": 94.52}, "branches": {"total": 13, "covered": 11, "skipped": 0, "pct": 84.61}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\EmailDomains.route.ts": {"lines": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Geolocation.route.ts": {"lines": {"total": 29, "covered": 29, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 29, "covered": 29, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\HomePorts.route.ts": {"lines": {"total": 18, "covered": 18, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 18, "covered": 18, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\InAppNotification.route.ts": {"lines": {"total": 46, "covered": 46, "skipped": 0, "pct": 100}, "functions": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}, "statements": {"total": 49, "covered": 49, "skipped": 0, "pct": 100}, "branches": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Kinesis.route.ts": {"lines": {"total": 189, "covered": 188, "skipped": 0, "pct": 99.47}, "functions": {"total": 38, "covered": 37, "skipped": 0, "pct": 97.36}, "statements": {"total": 201, "covered": 199, "skipped": 0, "pct": 99}, "branches": {"total": 121, "covered": 116, "skipped": 0, "pct": 95.86}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Log.route.ts": {"lines": {"total": 75, "covered": 74, "skipped": 0, "pct": 98.66}, "functions": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "statements": {"total": 76, "covered": 75, "skipped": 0, "pct": 98.68}, "branches": {"total": 33, "covered": 32, "skipped": 0, "pct": 96.96}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\NotificationAlert.route.ts": {"lines": {"total": 262, "covered": 257, "skipped": 0, "pct": 98.09}, "functions": {"total": 60, "covered": 59, "skipped": 0, "pct": 98.33}, "statements": {"total": 275, "covered": 270, "skipped": 0, "pct": 98.18}, "branches": {"total": 101, "covered": 88, "skipped": 0, "pct": 87.12}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\NotificationSummary.route.ts": {"lines": {"total": 197, "covered": 197, "skipped": 0, "pct": 100}, "functions": {"total": 45, "covered": 45, "skipped": 0, "pct": 100}, "statements": {"total": 215, "covered": 215, "skipped": 0, "pct": 100}, "branches": {"total": 83, "covered": 81, "skipped": 0, "pct": 97.59}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Organization.route.ts": {"lines": {"total": 71, "covered": 71, "skipped": 0, "pct": 100}, "functions": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}, "statements": {"total": 76, "covered": 76, "skipped": 0, "pct": 100}, "branches": {"total": 19, "covered": 19, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Permission.route.ts": {"lines": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Region.route.ts": {"lines": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\RegionGroup.route.ts": {"lines": {"total": 50, "covered": 50, "skipped": 0, "pct": 100}, "functions": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}, "statements": {"total": 52, "covered": 52, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Role.route.ts": {"lines": {"total": 133, "covered": 133, "skipped": 0, "pct": 100}, "functions": {"total": 46, "covered": 46, "skipped": 0, "pct": 100}, "statements": {"total": 161, "covered": 161, "skipped": 0, "pct": 100}, "branches": {"total": 31, "covered": 31, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\S3.route.ts": {"lines": {"total": 65, "covered": 65, "skipped": 0, "pct": 100}, "functions": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}, "statements": {"total": 65, "covered": 65, "skipped": 0, "pct": 100}, "branches": {"total": 19, "covered": 19, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Statistics.route.ts": {"lines": {"total": 25, "covered": 25, "skipped": 0, "pct": 100}, "functions": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "statements": {"total": 26, "covered": 26, "skipped": 0, "pct": 100}, "branches": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Thingsboard.route.ts": {"lines": {"total": 28, "covered": 27, "skipped": 0, "pct": 96.42}, "functions": {"total": 4, "covered": 3, "skipped": 0, "pct": 75}, "statements": {"total": 28, "covered": 27, "skipped": 0, "pct": 96.42}, "branches": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\TourGuide.route.ts": {"lines": {"total": 52, "covered": 51, "skipped": 0, "pct": 98.07}, "functions": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "statements": {"total": 52, "covered": 51, "skipped": 0, "pct": 98.07}, "branches": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\User.route.ts": {"lines": {"total": 398, "covered": 398, "skipped": 0, "pct": 100}, "functions": {"total": 57, "covered": 57, "skipped": 0, "pct": 100}, "statements": {"total": 440, "covered": 438, "skipped": 0, "pct": 99.54}, "branches": {"total": 132, "covered": 129, "skipped": 0, "pct": 97.72}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\VesselAis.route.ts": {"lines": {"total": 60, "covered": 59, "skipped": 0, "pct": 98.33}, "functions": {"total": 11, "covered": 10, "skipped": 0, "pct": 90.9}, "statements": {"total": 66, "covered": 64, "skipped": 0, "pct": 96.96}, "branches": {"total": 11, "covered": 10, "skipped": 0, "pct": 90.9}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\VesselLocation.route.ts": {"lines": {"total": 152, "covered": 150, "skipped": 0, "pct": 98.68}, "functions": {"total": 36, "covered": 33, "skipped": 0, "pct": 91.66}, "statements": {"total": 168, "covered": 162, "skipped": 0, "pct": 96.42}, "branches": {"total": 33, "covered": 30, "skipped": 0, "pct": 90.9}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\VesselManagement.route.ts": {"lines": {"total": 84, "covered": 83, "skipped": 0, "pct": 98.8}, "functions": {"total": 14, "covered": 14, "skipped": 0, "pct": 100}, "statements": {"total": 90, "covered": 87, "skipped": 0, "pct": 96.66}, "branches": {"total": 21, "covered": 18, "skipped": 0, "pct": 85.71}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Vessels.route.ts": {"lines": {"total": 48, "covered": 46, "skipped": 0, "pct": 95.83}, "functions": {"total": 14, "covered": 14, "skipped": 0, "pct": 100}, "statements": {"total": 55, "covered": 52, "skipped": 0, "pct": 94.54}, "branches": {"total": 27, "covered": 19, "skipped": 0, "pct": 70.37}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\index.ts": {"lines": {"total": 61, "covered": 61, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 61, "covered": 61, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\Artifact.route.ts": {"lines": {"total": 67, "covered": 66, "skipped": 0, "pct": 98.5}, "functions": {"total": 11, "covered": 10, "skipped": 0, "pct": 90.9}, "statements": {"total": 72, "covered": 69, "skipped": 0, "pct": 95.83}, "branches": {"total": 16, "covered": 14, "skipped": 0, "pct": 87.5}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\Kinesis.route.ts": {"lines": {"total": 47, "covered": 46, "skipped": 0, "pct": 97.87}, "functions": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}, "statements": {"total": 49, "covered": 48, "skipped": 0, "pct": 97.95}, "branches": {"total": 16, "covered": 15, "skipped": 0, "pct": 93.75}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\NotificationSummary.route.ts": {"lines": {"total": 34, "covered": 34, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 34, "covered": 34, "skipped": 0, "pct": 100}, "branches": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\User.route.ts": {"lines": {"total": 50, "covered": 50, "skipped": 0, "pct": 100}, "functions": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "statements": {"total": 52, "covered": 52, "skipped": 0, "pct": 100}, "branches": {"total": 28, "covered": 28, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\VesselLocation.route.ts": {"lines": {"total": 76, "covered": 75, "skipped": 0, "pct": 98.68}, "functions": {"total": 15, "covered": 14, "skipped": 0, "pct": 93.33}, "statements": {"total": 82, "covered": 79, "skipped": 0, "pct": 96.34}, "branches": {"total": 24, "covered": 20, "skipped": 0, "pct": 83.33}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\index.v2.ts": {"lines": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\ApiKey.service.ts": {"lines": {"total": 41, "covered": 41, "skipped": 0, "pct": 100}, "functions": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}, "statements": {"total": 55, "covered": 55, "skipped": 0, "pct": 100}, "branches": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\ArtifactFavourites.service.ts": {"lines": {"total": 64, "covered": 64, "skipped": 0, "pct": 100}, "functions": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "statements": {"total": 76, "covered": 76, "skipped": 0, "pct": 100}, "branches": {"total": 18, "covered": 18, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\ArtifactFlag.service.ts": {"lines": {"total": 33, "covered": 33, "skipped": 0, "pct": 100}, "functions": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}, "statements": {"total": 37, "covered": 37, "skipped": 0, "pct": 100}, "branches": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\RegionGroup.service.ts": {"lines": {"total": 44, "covered": 44, "skipped": 0, "pct": 100}, "functions": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}, "statements": {"total": 51, "covered": 51, "skipped": 0, "pct": 100}, "branches": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\Stream.service.ts": {"lines": {"total": 45, "covered": 45, "skipped": 0, "pct": 100}, "functions": {"total": 15, "covered": 15, "skipped": 0, "pct": 100}, "statements": {"total": 54, "covered": 54, "skipped": 0, "pct": 100}, "branches": {"total": 25, "covered": 25, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\Thingsboard.service.ts": {"lines": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "functions": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "statements": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\Vessel.service.ts": {"lines": {"total": 160, "covered": 160, "skipped": 0, "pct": 100}, "functions": {"total": 19, "covered": 19, "skipped": 0, "pct": 100}, "statements": {"total": 167, "covered": 167, "skipped": 0, "pct": 100}, "branches": {"total": 100, "covered": 100, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\VesselLocation.service.ts": {"lines": {"total": 81, "covered": 81, "skipped": 0, "pct": 100}, "functions": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "statements": {"total": 83, "covered": 83, "skipped": 0, "pct": 100}, "branches": {"total": 25, "covered": 25, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\Email.ts": {"lines": {"total": 30, "covered": 30, "skipped": 0, "pct": 100}, "functions": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}, "statements": {"total": 30, "covered": 30, "skipped": 0, "pct": 100}, "branches": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\endpointIds.ts": {"lines": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\functions.ts": {"lines": {"total": 261, "covered": 261, "skipped": 0, "pct": 100}, "functions": {"total": 50, "covered": 50, "skipped": 0, "pct": 100}, "statements": {"total": 303, "covered": 302, "skipped": 0, "pct": 99.66}, "branches": {"total": 165, "covered": 164, "skipped": 0, "pct": 99.39}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\permissions.ts": {"lines": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\roles.ts": {"lines": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\seaPorts.ts": {"lines": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\staticMap.ts": {"lines": {"total": 110, "covered": 110, "skipped": 0, "pct": 100}, "functions": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}, "statements": {"total": 114, "covered": 114, "skipped": 0, "pct": 100}, "branches": {"total": 71, "covered": 71, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\timezonesList.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\userEndpointRestrictions.ts": {"lines": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}}