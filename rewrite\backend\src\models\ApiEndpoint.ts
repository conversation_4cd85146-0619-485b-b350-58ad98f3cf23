import mongoose from "mongoose";
import db from "../modules/db";
import { IApiEndpoint } from "src/interfaces/ApiEndpoint";

const apiEndpointSchema = new mongoose.Schema({
    endpoint_id: { type: Number, required: true, unique: true },
    name: { type: String, required: true },
    category: { type: String, required: true },
});

const ApiEndpoint = db.qm.model<IApiEndpoint>("ApiEndpoint", apiEndpointSchema, "api_endpoints");

export default ApiEndpoint;
