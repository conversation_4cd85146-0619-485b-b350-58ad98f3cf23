import rateLimit from "express-rate-limit";
import express, { Request, Response } from "express";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import NotificationSummary from "../models/NotificationSummary";
import isAuthenticated from "../middlewares/auth";
import { validateData } from "../middlewares/validator";
import { body, param, query } from "express-validator";
import { sendEmail } from "../modules/email";
import { SUMMARY_SUBSCRIPTION_EMAIL_CONTENT } from "../utils/Email";
import jwt from "jsonwebtoken";
import { generateUnsubscribeToken, canAccessVessel } from "../utils/functions";
import EmailDomains from "../models/EmailDomains";
import { permissions } from "../utils/permissions";
import User from "../models/User";
import mongoose, { isValidObjectId } from "mongoose";
import vesselService from "../services/Vessel.service";
import { INotificationSummary } from "../interfaces/Notification";
import { IEmailDomain } from "../interfaces/Email";
import { IQueryFilter } from "src/interfaces/Common";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_NOTIFICATION_SUMMARIES), isAuthenticated, async (req: Request, res: Response) => {
    const page: number = parseInt(req.query.page as string) || 1;
    const page_size: number = parseInt(req.query.page_size as string) || 10;

    try {
        const totalDocuments: number = await NotificationSummary.countDocuments({ created_by: req.user._id });
        const totalPages: number = Math.ceil(totalDocuments / page_size);
        const notificationSummaries: INotificationSummary[] = await NotificationSummary.find({ created_by: req.user._id })
            .skip((page - 1) * page_size)
            .limit(page_size);

        res.json({
            data: notificationSummaries,
            total_pages: totalPages,
            total_documents: totalDocuments,
            current_page: page,
            next_page: page < totalPages ? page + 1 : null,
            previous_page: page > 1 ? page - 1 : null,
        });
    } catch (err: any) {
        res.status(500).json({ message: err.message });
    }
});

router.get(
    "/:id",
    assignEndpointId.bind(this, endpointIds.FETCH_NOTIFICATION_SUMMARIES),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value: string, { path }: { path: string }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const notificationSummary: INotificationSummary | null = await NotificationSummary.findOne({
                _id: req.params.id,
                created_by: req.user._id,
            });
            if (!notificationSummary) {
                return res.status(404).json({ message: "Notification Summary not found" });
            }
            res.json(notificationSummary);
        } catch (err: any) {
            res.status(500).json({ message: err.message });
        }
    },
);

router.patch(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_NOTIFICATION_SUMMARIES),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value: string, { path }: { path: string }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
        body("vessel_ids")
            .isArray()
            .notEmpty()
            .optional()
            .withMessage((value: string, { path }: { path: string }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((vessels: string[]) => {
                if (vessels.length === 0) {
                    throw new Error(`At least one Vessel ID is required`);
                }
                return true;
            }),
        body("vessel_ids.*")
            .optional()
            .custom((value: string) => {
                if (value === "all") return true;
                return isValidObjectId(value);
            })
            .withMessage((value: string, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`),
        body("preference")
            .optional()
            .isArray()
            .notEmpty()
            .withMessage(() => `preference must be an array`)
            .custom((preferences: string[]) => {
                const validPreferences = ["daily", "weekly", "monthly"];
                if (!preferences.every((p: string) => validPreferences.includes(p))) {
                    throw new Error(`Invalid preference value(s) provided`);
                }
                return true;
            }),
        body("receivers")
            .optional()
            .isArray()
            .withMessage(() => `receivers must be an array`)
            .custom((receivers: string[]) => {
                if (receivers.length > 20) {
                    throw new Error("A maximum of 20 receivers are allowed");
                }
                for (let email of receivers) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error(`Invalid email address: ${email}`);
                    }
                }
                return true;
            }),
        body("is_enabled")
            .optional()
            .custom((value: number) => {
                if (value === 1 || value === 0) {
                    return true;
                }
                throw new Error("is_enabled must be 0 or 1");
            }),
        body("title")
            .isArray()
            .notEmpty()
            .optional()
            .withMessage((value: string, { path }: { path: string }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((tit: string[]) => {
                if (tit.length === 0) {
                    throw new Error(`At least one Title is required`);
                }
                return true;
            }),
    ]),
    async (req: Request, res: Response) => {
        const {
            receivers,
            vessel_ids,
            preference,
            is_enabled,
            title,
        }: { vessel_ids?: string[]; preference?: string[]; receivers?: string[]; is_enabled?: number; title?: string[] } = req.body;

        const updateData: IQueryFilter = {};
        if (vessel_ids) {
            updateData.vessel_ids = vessel_ids.map((id: string) => {
                if (id === "all") return "all";
                return new mongoose.Types.ObjectId(id);
            });
        }
        if (preference) updateData.preference = preference;
        if (receivers) updateData.receivers = receivers;
        if (typeof is_enabled !== "undefined") updateData.is_enabled = is_enabled;
        if (title) updateData.title = title;

        /** Ensure user cannot subscribe to vessels not assigned to them */
        if (vessel_ids) {
            const vessels = await vesselService.find({ _id: { $in: vessel_ids.filter((id: string) => id !== "all") } });

            const hasUnauthorizedVessels = vessels.some((v) => !canAccessVessel(req, v));
            if (hasUnauthorizedVessels) {
                return res.status(403).json({ message: "Not allowed to subscribe to vessels not assigned to you" });
            }
        }

        if (receivers) {
            const getDomains: IEmailDomain[] = await EmailDomains.find({});
            const domains: string[] = getDomains.map((domain) => domain.domain);
            if (!req.user.email) {
                return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
            }
            const userEmailDomain = req.user.email.split("@")[1];
            if (!domains.includes(userEmailDomain)) {
                return res.status(400).json({ message: "User email domain is not allowed." });
            }
            const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions.additionalEmailAddressesPrivilege);
            const allReceiversValid = receivers.every((email: string) => {
                const receiverDomain = email.split("@")[1];
                return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
            });
            if (!allReceiversValid) {
                return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
            }
            const notificationSummary: INotificationSummary | null = await NotificationSummary.findById(req.params.id);
            if (!notificationSummary) {
                return res.status(404).json({ message: "Notification Alert not found" });
            }
            //check if new addition is made in payload separate that out please for me
            const newReceivers: string[] = receivers.filter((receiver) => !notificationSummary.receivers.includes(receiver));
            const { email } = req.user;

            for (let receiver of newReceivers) {
                const token: string = generateUnsubscribeToken(receiver, notificationSummary._id.toString());

                const payload: { addBy: string; vessel: string; preference: string; link: string } = {
                    addBy: email || "Unknown",
                    vessel: Array.isArray(notificationSummary.title) ? notificationSummary.title.join(",") : notificationSummary.title || "Unknown",
                    preference: Array.isArray(notificationSummary.preference)
                        ? notificationSummary.preference.join("/")
                        : notificationSummary.preference || "Unknown",
                    link: `${process.env.API_URL}/summaryReports/unsubscribe/email?token=${token}`,
                };
                const emailBody = SUMMARY_SUBSCRIPTION_EMAIL_CONTENT(
                    payload,
                    new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear(),
                );
                sendEmail({
                    to: receiver,
                    subject: "Notification Alert",
                    html: emailBody,
                });
            }
        }
        try {
            const notificationSummary: INotificationSummary | null = await NotificationSummary.findOneAndUpdate(
                { _id: req.params.id, created_by: req.user._id },
                updateData,
                {
                    new: true,
                },
            );
            if (!notificationSummary) {
                return res.status(404).json({ message: "Notification Summary not found" });
            }
            res.json(notificationSummary);
        } catch (err: any) {
            res.status(500).json({ message: err.message });
        }
    },
);

router.delete(
    "/:id",
    assignEndpointId.bind(this, endpointIds.DELETE_NOTIFICATION_SUMMARIES),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value: string, { path }: { path: string }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const notificationSummary: INotificationSummary | null = await NotificationSummary.findOneAndDelete({
                _id: req.params.id,
                created_by: req.user._id,
            });
            if (!notificationSummary) {
                return res.status(404).json({ message: "Notification Summary not found" });
            }
            res.json(notificationSummary);
        } catch (err: any) {
            res.status(500).json({ message: err.message });
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_NOTIFICATION_SUMMARIES),
    isAuthenticated,
    validateData.bind(this, [
        body("vessel_ids")
            .isArray()
            .notEmpty()
            .withMessage((value: string, { path }: { path: string }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((vessels: string[]) => {
                if (vessels.length === 0) {
                    throw new Error(`At least one Vessel ID is required`);
                }
                return true;
            }),
        body("vessel_ids.*")
            .custom((value: string) => {
                if (value === "all") return true;
                return isValidObjectId(value);
            })
            .withMessage((value: string, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`),
        body("preference")
            .notEmpty()
            .isArray()
            .withMessage(() => `preference must be an array`),
        body("receivers")
            .isArray()
            .optional()
            .withMessage(() => `receivers must be an array`)
            .custom((receivers: string[]) => {
                if (receivers.length > 20) {
                    throw new Error("A maximum of 20 receivers are allowed");
                }
                for (let email of receivers) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error(`Invalid email address: ${email}`);
                    }
                }
                return true;
            }),
        body("is_enabled")
            .optional()
            .custom((value: number) => {
                if (value === 1 || value === 0) {
                    return true;
                }
                throw new Error("is_enabled must be 0 or 1");
            }),
        body("title")
            .isArray()
            .notEmpty()
            .withMessage((value: string, { path }: { path: string }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((tit: string[]) => {
                if (tit.length === 0) {
                    throw new Error(`At least one Title is required`);
                }
                return true;
            }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const {
                vessel_ids,
                preference,
                receivers,
                is_enabled,
                title,
            }: { vessel_ids: string[]; preference: string[]; receivers?: string[]; is_enabled?: number; title: string[] } = req.body;
            let notificationSummary: INotificationSummary;

            /** Ensure user cannot subscribe to vessels not assigned to them */
            const vessels = await vesselService.find({ _id: { $in: vessel_ids.filter((id: string) => id !== "all") } });

            const hasUnauthorizedVessels = vessels.some((v) => !canAccessVessel(req, v));
            if (hasUnauthorizedVessels) {
                return res.status(403).json({ message: "Not allowed to subscribe to vessels not assigned to you" });
            }

            if (receivers) {
                const getDomains: IEmailDomain[] = await EmailDomains.find({});
                const domains: string[] = getDomains.map((domain) => domain.domain);
                if (!req.user.email) {
                    return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
                }
                const userEmailDomain = req.user.email.split("@")[1];
                if (!domains.includes(userEmailDomain)) {
                    return res.status(400).json({ message: "User email domain is not allowed." });
                }
                const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions.additionalEmailAddressesPrivilege);
                const allReceiversValid = receivers.every((email: string) => {
                    const receiverDomain = email.split("@")[1];
                    return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
                });
                if (!allReceiversValid) {
                    return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
                }
            }

            const processedVesselIds: (string | mongoose.Types.ObjectId)[] = vessel_ids.map((id: string) => {
                if (id === "all") return "all";
                return new mongoose.Types.ObjectId(id);
            });

            if (is_enabled) {
                notificationSummary = await NotificationSummary.create({
                    vessel_ids: processedVesselIds,
                    preference,
                    receivers,
                    title,
                    is_enabled: is_enabled === 1,
                    created_by: req.user._id,
                });
            } else {
                notificationSummary = await NotificationSummary.create({
                    vessel_ids: processedVesselIds,
                    preference,
                    receivers,
                    title,
                    created_by: req.user._id,
                });
            }
            if (receivers) {
                if (receivers.length > 0 && notificationSummary._id) {
                    const { email } = req.user;
                    for (let receiver of receivers) {
                        const token: string = generateUnsubscribeToken(receiver, notificationSummary._id.toString());
                        const payload: { addBy: string; vessel: string; preference: string; link: string } = {
                            addBy: email || "Unknown",
                            vessel: Array.isArray(notificationSummary.title)
                                ? notificationSummary.title.join(",")
                                : notificationSummary.title || "Unknown",
                            preference: Array.isArray(notificationSummary.preference)
                                ? notificationSummary.preference.join("/")
                                : notificationSummary.preference || "Unknown",
                            link: `${process.env.API_URL}/summaryReports/unsubscribe/email?token=${token}`,
                        };
                        const emailBody = SUMMARY_SUBSCRIPTION_EMAIL_CONTENT(
                            payload,
                            new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear(),
                        );
                        sendEmail({
                            to: receiver,
                            subject: "Notification Alert",
                            html: emailBody,
                        });
                    }
                }
            }

            res.status(201).json(notificationSummary);
        } catch (err: any) {
            res.status(500).json({ message: err.message });
        }
    },
);

router.get(
    "/unsubscribe/email",
    assignEndpointId.bind(this, endpointIds.UNSUBSCRIBE_NOTIFICATION_SUMMARIES),
    validateData.bind(this, [
        query("token")
            .isString()
            .notEmpty()
            .withMessage((value: string, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        // decrypt jwt_token to get email and notification_summary id in it
        const { token } = req.query as { token: string };
        const { notificationId, email } = jwt.verify(token, process.env.JWT_SECRET as string) as { notificationId: string; email: string };

        try {
            const notificationAlert: INotificationSummary | null = await NotificationSummary.findById(notificationId);
            if (!notificationAlert) {
                return res.redirect(
                    `${process.env.APP_URL}/subscription?status=404&title=Something Went Wrong&message=Notification Summary does not exist`,
                );
            }

            if (!notificationAlert.receivers.includes(email)) {
                const user = await User.findOne({ email });

                if (user && user._id.toString() === notificationAlert.created_by.toString()) {
                    await NotificationSummary.updateOne(
                        { _id: notificationId },
                        { $set: { is_enabled: false, updated_at: new Date().toISOString() } },
                    );

                    return res.redirect(
                        `${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification summary emails `,
                    );
                }

                return res.redirect(
                    `${process.env.APP_URL}/subscription?status=401&title=Email Removed&message=This email does not exist or already removed from notification alert`,
                );
            }

            const updateRes = await NotificationSummary.updateOne({ _id: notificationId }, { $pull: { receivers: email } });

            if (!updateRes.modifiedCount) {
                return res.redirect(
                    `${process.env.APP_URL}/subscription?status=404&title=Email Removed&message=This email does not exist or already removed from notification summary`,
                );
            }

            return res.redirect(
                `${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification alert emails `,
            );
        } catch (err: any) {
            res.status(500).json({ message: err.message });
        }
    },
);

export default router;
