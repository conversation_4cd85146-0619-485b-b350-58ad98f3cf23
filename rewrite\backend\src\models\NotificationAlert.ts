import mongoose from "mongoose";
import db from "../modules/db";
import User from "./User";
import { INotificationAlert } from "../interfaces/Notification";

const NotificationAlertsSchema = new mongoose.Schema({
    super_category: { type: [String], required: true },
    sub_category: { type: [String], required: true },
    country_flags: { type: Array, default: [] },
    type: { type: String, required: true, enum: ["email", "app", "both"] },
    title: { type: [String], required: true },
    unit_id: { type: [String], required: false }, // kept for backward compatibility
    vessel_ids: {
        type: [mongoose.Schema.Types.Mixed],
        required: true,
        validate: {
            validator: function (arr: (string | mongoose.Types.ObjectId)[]) {
                return arr.every((item: string | mongoose.Types.ObjectId) => item === "all" || mongoose.Types.ObjectId.isValid(item));
            },
            message: 'vessel_ids must contain valid ObjectIds or "all"',
        },
    },
    receivers: [{ type: String }], // will be used in v2 to send to multiple users
    created_at: { type: Date, default: () => new Date().toISOString() },
    updated_at: { type: Date, default: () => new Date().toISOString() },
    is_enabled: { type: Boolean, default: true },
    created_by: { type: mongoose.Schema.Types.ObjectId, ref: User },
});

const NotificationAlert = db.qm.model<INotificationAlert>("NotificationAlert", NotificationAlertsSchema, "notifications_alerts");

export default NotificationAlert;
